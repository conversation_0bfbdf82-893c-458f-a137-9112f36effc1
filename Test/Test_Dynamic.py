#!/usr/bin/env python3
"""
动态模型测试脚本 - Test_Dynamic.py
根据IDEA.md第2.5节规范实现

主要功能：
1. 加载训练好的动态任务图模型参数
2. 在测试集(s4)上进行推理
3. 计算所有核心评估指标（IoU, Accuracy等）
4. 生成详细的评估报告和可视化图表
5. 保存原始预测序列和每个测试样本的偏差Diff_k和调整量ΔW_k
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import json
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入评估指标
from src.utils.metrics import (
    greedy_merge, segment_level_iou, frame_accuracy, 
    action_set_coverage, lev_norm, compute_metrics
)

# 设置可视化风格
sns.set_theme(style="whitegrid")

# 全局配置
DATA_ROOT = "/data2/syd_data/Breakfast_Data"
LABEL_MAP_PATH = os.path.join(DATA_ROOT, "label_map.json")
TEST_DATA_DIR = os.path.join(DATA_ROOT, "breakfast_data_npy")
TEST_LABEL_DIR = os.path.join(DATA_ROOT, "segmentation_coarse_npy")

# 输入路径配置（从训练步骤的输出）
DYNAMIC_MODEL_DIR = os.path.join(DATA_ROOT, "Outputs", "Dynamic", "Model_parameters")

# 输出路径配置（符合IDEA.md第5.4章规范）
OUTPUT_BASE = os.path.join(DATA_ROOT, "Outputs", "Evaluation_Result")

# 生成时间戳ID
TIMESTAMP_ID = datetime.now().strftime("%Y%m%d-%H%M%S")


class MLPDiff(nn.Module):
    """差分更新MLP网络（用于推理）"""
    
    def __init__(self, D: int, M: int, H: int = 256, alpha: float = 0.05):
        super().__init__()
        self.alpha = alpha
        
        self.mlp = nn.Sequential(
            nn.Linear(D, H),
            nn.ReLU(),
            nn.Linear(H, M),
            nn.Tanh()
        )
    
    def forward(self, diff: torch.Tensor) -> torch.Tensor:
        return self.alpha * self.mlp(diff)


class DynamicModel(nn.Module):
    """动态任务图模型（用于推理）"""
    
    def __init__(self, edge_weights: torch.Tensor, prototypes: torch.Tensor, 
                 D: int, M: int, H: int = 256, alpha: float = 0.05):
        super().__init__()
        
        self.M = M
        self.register_buffer('edge_weights', edge_weights)
        self.register_buffer('prototypes', prototypes)
        
        self.mlp_diff = MLPDiff(D, M, H, alpha)
        
        # 推理时的累积图权重
        self.W = None
        self.reset()
    
    def reset(self):
        """重置推理时的图状态"""
        self.W = self.edge_weights.clone()
    
    def forward(self, features: torch.Tensor, current_action: int) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        前向传播（推理模式）
        
        Returns:
            logits: 下一动作的logits
            diff: 计算的差分特征
            delta_W: 权重调整量
        """
        # 计算差分特征
        diff = torch.abs(features - self.prototypes[current_action])
        
        # 通过MLP预测权重调整量
        delta_W = self.mlp_diff(diff.unsqueeze(0)).squeeze(0)  # (M,)
        
        # 更新累积图权重
        self.W[current_action] += delta_W
        
        # 计算下一步logits
        logits = self.W[current_action]
        
        return logits, diff, delta_W


class DynamicModelTester:
    """动态模型测试器"""
    
    def __init__(self):
        self.label_map = self.load_label_map()
        self.M = len(self.label_map)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建输出目录
        os.makedirs(OUTPUT_BASE, exist_ok=True)
        
        print(f"🧪 动态模型测试器初始化")
        print(f"   - 动作类别数: {self.M}")
        print(f"   - 设备: {self.device}")
        print(f"   - 时间戳ID: {TIMESTAMP_ID}")
    
    def load_label_map(self) -> Dict[str, str]:
        """加载标签映射"""
        with open(LABEL_MAP_PATH, 'r', encoding='utf-8') as f:
            label_map = json.load(f)
        return label_map
    
    def load_dynamic_model(self) -> DynamicModel:
        """加载训练好的动态模型"""
        # 查找最新的动态模型文件
        dynamic_files = [f for f in os.listdir(DYNAMIC_MODEL_DIR) if f.startswith('dynamic_model_full_') and f.endswith('.pt')]
        
        if not dynamic_files:
            raise FileNotFoundError(f"未找到动态模型文件在 {DYNAMIC_MODEL_DIR}")
        
        latest_file = sorted(dynamic_files)[-1]
        model_path = os.path.join(DYNAMIC_MODEL_DIR, latest_file)
        
        # 加载模型数据
        model_data = torch.load(model_path, map_location='cpu')
        edge_weights = model_data['edge_weights']
        prototypes = model_data['prototypes']
        feature_dim = model_data['feature_dim']
        hidden_dim = model_data['hidden_dim']
        alpha = model_data['alpha']
        
        print(f"📥 加载动态模型: {model_path}")
        print(f"   - 边权重形状: {edge_weights.shape}")
        print(f"   - 原型形状: {prototypes.shape}")
        print(f"   - 特征维度: {feature_dim}")
        print(f"   - 隐藏维度: {hidden_dim}")
        print(f"   - Alpha: {alpha}")
        
        # 创建模型
        model = DynamicModel(edge_weights, prototypes, feature_dim, self.M, hidden_dim, alpha).to(self.device)
        
        # 加载训练好的MLP权重
        model.load_state_dict(model_data['model_state_dict'])
        model.eval()
        
        return model
    
    def find_test_files(self) -> List[Tuple[str, str]]:
        """查找测试文件对（特征文件，标签文件）"""
        file_pairs = []
        
        # 扫描s4的测试数据
        subject = "s4"
        subject_feat_dir = os.path.join(TEST_DATA_DIR, subject)
        subject_label_dir = os.path.join(TEST_LABEL_DIR, f"{subject}_label")
        
        if not os.path.exists(subject_feat_dir) or not os.path.exists(subject_label_dir):
            raise FileNotFoundError(f"测试数据目录不存在: {subject_feat_dir} 或 {subject_label_dir}")
        
        # 遍历任务目录
        for task_dir in os.listdir(subject_feat_dir):
            feat_task_path = os.path.join(subject_feat_dir, task_dir)
            label_task_path = os.path.join(subject_label_dir, task_dir)
            
            if not os.path.isdir(feat_task_path) or not os.path.isdir(label_task_path):
                continue
            
            # 匹配特征文件和标签文件
            for feat_file in os.listdir(feat_task_path):
                if feat_file.endswith('.npy'):
                    feat_path = os.path.join(feat_task_path, feat_file)
                    label_file = feat_file  # 假设文件名相同
                    label_path = os.path.join(label_task_path, label_file)
                    
                    if os.path.exists(label_path):
                        file_pairs.append((feat_path, label_path))
        
        print(f"📁 找到 {len(file_pairs)} 个测试文件对")
        return file_pairs
    
    def predict_sequence(self, model: DynamicModel, features: torch.Tensor, 
                        initial_action: int) -> Tuple[List[int], List[Dict]]:
        """
        对单个序列进行预测
        
        Args:
            model: 动态模型
            features: 特征序列 (N, D)
            initial_action: 初始动作（第一帧的真实标签）
            
        Returns:
            predictions: 预测的动作序列
            debug_info: 每一步的调试信息（包含Diff_k和ΔW_k）
        """
        model.eval()
        model.reset()  # 重置图状态
        
        predictions = [initial_action]
        debug_info = []
        current_action = initial_action
        
        with torch.no_grad():
            for k in range(len(features) - 1):
                V_k = features[k]
                
                # 进行预测并获取调试信息
                logits, diff_k, delta_W_k = model(V_k, current_action)
                
                # 预测下一动作
                next_action = torch.argmax(logits).item()
                predictions.append(next_action)
                
                # 保存调试信息
                debug_info.append({
                    'step': k,
                    'current_action': current_action,
                    'next_action': next_action,
                    'diff_k': diff_k.cpu().numpy().tolist(),
                    'delta_W_k': delta_W_k.cpu().numpy().tolist(),
                    'logits': logits.cpu().numpy().tolist()
                })
                
                # 更新当前动作为预测值（非Teacher Forcing）
                current_action = next_action
        
        return predictions, debug_info

    def run_inference(self, model: DynamicModel, file_pairs: List[Tuple[str, str]]) -> Tuple[List[List[int]], List[List[int]], List[List[Dict]]]:
        """
        在所有测试文件上运行推理

        Returns:
            (predictions, ground_truths, all_debug_info): 预测序列列表、真实序列列表和调试信息列表
        """
        print("🔄 开始推理测试...")

        all_predictions = []
        all_ground_truths = []
        all_debug_info = []

        for i, (feat_path, label_path) in enumerate(file_pairs):
            try:
                # 加载数据
                features = torch.from_numpy(np.load(feat_path)).float().to(self.device)
                labels = np.load(label_path)

                # 确保维度匹配
                min_len = min(len(features), len(labels))
                features = features[:min_len]
                labels = labels[:min_len]

                if len(features) < 2:
                    print(f"⚠️ 跳过过短序列: {feat_path}")
                    continue

                # 进行预测
                initial_action = int(labels[0])
                predictions, debug_info = self.predict_sequence(model, features, initial_action)

                # 确保预测和真实序列长度一致
                min_pred_len = min(len(predictions), len(labels))
                predictions = predictions[:min_pred_len]
                ground_truth = labels[:min_pred_len].tolist()

                all_predictions.append(predictions)
                all_ground_truths.append(ground_truth)
                all_debug_info.append(debug_info)

                if (i + 1) % 10 == 0:
                    print(f"   处理进度: {i + 1}/{len(file_pairs)}")

            except Exception as e:
                print(f"⚠️ 处理文件失败 {feat_path}: {e}")
                continue

        print(f"✅ 推理完成: {len(all_predictions)} 个序列")
        return all_predictions, all_ground_truths, all_debug_info

    def compute_evaluation_metrics(self, predictions: List[List[int]],
                                 ground_truths: List[List[int]]) -> Dict:
        """计算评估指标"""
        print("📊 计算评估指标...")

        # 使用现有的评估指标函数
        metrics = compute_metrics(predictions, ground_truths)

        # 添加更详细的统计信息
        detailed_metrics = {
            'segment_iou_mean': metrics['segment_iou_mean'],
            'frame_accuracy_mean': metrics['frame_accuracy_mean'],
            'action_coverage_mean': metrics['action_coverage_mean'],
            'lev_mean': metrics['lev_mean'],
            'num_sequences': len(predictions),
            'total_frames': sum(len(pred) for pred in predictions),
            'avg_sequence_length': sum(len(pred) for pred in predictions) / len(predictions) if predictions else 0
        }

        print(f"   ✅ 评估指标计算完成:")
        print(f"      - 时序段级IoU: {detailed_metrics['segment_iou_mean']:.4f}")
        print(f"      - 帧级准确度: {detailed_metrics['frame_accuracy_mean']:.4f}")
        print(f"      - 动作覆盖率: {detailed_metrics['action_coverage_mean']:.4f}")
        print(f"      - 归一化编辑距离: {detailed_metrics['lev_mean']:.4f}")

        return detailed_metrics

    def save_results(self, predictions: List[List[int]], ground_truths: List[List[int]],
                    debug_info: List[List[Dict]], metrics: Dict):
        """保存测试结果"""
        print("💾 保存测试结果...")

        # 1. 保存原始预测序列
        predictions_path = os.path.join(OUTPUT_BASE, f"dynamic_predictions_{TIMESTAMP_ID}.json")
        with open(predictions_path, 'w', encoding='utf-8') as f:
            json.dump({
                'predictions': predictions,
                'ground_truths': ground_truths,
                'timestamp_id': TIMESTAMP_ID,
                'model_type': 'dynamic',
                'label_map': self.label_map
            }, f, indent=2)

        # 2. 保存调试信息（Diff_k和ΔW_k）
        debug_path = os.path.join(OUTPUT_BASE, f"dynamic_debug_info_{TIMESTAMP_ID}.json")
        with open(debug_path, 'w', encoding='utf-8') as f:
            json.dump({
                'debug_info': debug_info,
                'timestamp_id': TIMESTAMP_ID,
                'description': 'Contains Diff_k and delta_W_k for each test sample'
            }, f, indent=2)

        # 3. 保存评估指标
        metrics_path = os.path.join(OUTPUT_BASE, f"dynamic_metrics_{TIMESTAMP_ID}.json")
        with open(metrics_path, 'w', encoding='utf-8') as f:
            json.dump(metrics, f, indent=2)

        # 4. 保存CSV格式的评估结果
        csv_path = os.path.join(OUTPUT_BASE, f"dynamic_evaluation_{TIMESTAMP_ID}.csv")
        with open(csv_path, 'w', encoding='utf-8') as f:
            f.write("model_name,metric_name,point_estimate\n")
            f.write(f"Dynamic,Segment_IoU,{metrics['segment_iou_mean']:.6f}\n")
            f.write(f"Dynamic,Frame_Accuracy,{metrics['frame_accuracy_mean']:.6f}\n")
            f.write(f"Dynamic,Action_Coverage,{metrics['action_coverage_mean']:.6f}\n")
            f.write(f"Dynamic,Normalized_Edit_Distance,{metrics['lev_mean']:.6f}\n")

        print(f"   ✅ 预测序列已保存: {predictions_path}")
        print(f"   ✅ 调试信息已保存: {debug_path}")
        print(f"   ✅ 评估指标已保存: {metrics_path}")
        print(f"   ✅ CSV结果已保存: {csv_path}")

    def generate_visualizations(self, predictions: List[List[int]],
                              ground_truths: List[List[int]],
                              debug_info: List[List[Dict]], metrics: Dict):
        """生成可视化图表"""
        print("📊 生成可视化图表...")

        # 1. 评估指标柱状图
        plt.figure(figsize=(10, 6))

        metric_names = ['Segment IoU', 'Frame Accuracy', 'Action Coverage', 'Norm Edit Dist']
        metric_values = [
            metrics['segment_iou_mean'],
            metrics['frame_accuracy_mean'],
            metrics['action_coverage_mean'],
            metrics['lev_mean']
        ]

        bars = plt.bar(metric_names, metric_values, color=['skyblue', 'lightgreen', 'lightcoral', 'gold'])
        plt.ylabel('Score')
        plt.title('Dynamic Model Evaluation Metrics')
        plt.ylim(0, 1)

        # 在柱状图上添加数值标签
        for bar, value in zip(bars, metric_values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{value:.3f}', ha='center', va='bottom')

        plt.tight_layout()
        metrics_plot_path = os.path.join(OUTPUT_BASE, f"dynamic_metrics_plot_{TIMESTAMP_ID}.png")
        plt.savefig(metrics_plot_path, dpi=300, bbox_inches='tight')
        plt.close()

        # 2. 权重调整量分布图
        if debug_info:
            plt.figure(figsize=(12, 6))

            # 收集所有ΔW_k的统计信息
            all_delta_W_norms = []
            for seq_debug in debug_info:
                for step_info in seq_debug:
                    delta_W = np.array(step_info['delta_W_k'])
                    all_delta_W_norms.append(np.linalg.norm(delta_W))

            plt.hist(all_delta_W_norms, bins=30, alpha=0.7, color='purple')
            plt.xlabel('||ΔW_k|| (L2 Norm of Weight Adjustment)')
            plt.ylabel('Frequency')
            plt.title('Dynamic Weight Adjustment Distribution')
            plt.grid(True, alpha=0.3)

            plt.tight_layout()
            delta_W_dist_path = os.path.join(OUTPUT_BASE, f"dynamic_delta_W_distribution_{TIMESTAMP_ID}.png")
            plt.savefig(delta_W_dist_path, dpi=300, bbox_inches='tight')
            plt.close()

        print(f"   ✅ 可视化图表已保存到: {OUTPUT_BASE}")

    def run_testing(self):
        """执行完整的测试流程"""
        print("=" * 80)
        print("🧪 动态模型测试开始")
        print("=" * 80)

        # 1. 加载模型
        model = self.load_dynamic_model()

        # 2. 查找测试文件
        file_pairs = self.find_test_files()

        # 3. 运行推理
        predictions, ground_truths, debug_info = self.run_inference(model, file_pairs)

        if not predictions:
            print("❌ 没有成功的预测结果!")
            return

        # 4. 计算评估指标
        metrics = self.compute_evaluation_metrics(predictions, ground_truths)

        # 5. 保存结果
        self.save_results(predictions, ground_truths, debug_info, metrics)

        # 6. 生成可视化
        self.generate_visualizations(predictions, ground_truths, debug_info, metrics)

        print("=" * 80)
        print("✅ 动态模型测试完成!")
        print(f"📁 输出目录: {OUTPUT_BASE}")
        print(f"🏷️ 时间戳ID: {TIMESTAMP_ID}")
        print("=" * 80)


def main():
    """主函数"""
    tester = DynamicModelTester()
    tester.run_testing()


if __name__ == "__main__":
    main()
