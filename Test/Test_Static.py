#!/usr/bin/env python3
"""
静态模型测试脚本 - Test_Static.py
根据IDEA.md第2.5节规范实现

主要功能：
1. 加载训练好的静态任务图模型参数
2. 在测试集(s4)上进行推理
3. 计算所有核心评估指标（IoU, Accuracy等）
4. 生成详细的评估报告和可视化图表
5. 保存原始预测序列供后续对比分析使用
"""

import os
import sys
import torch
import torch.nn.functional as F
import numpy as np
import json
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入评估指标
from src.utils.metrics import (
    greedy_merge, segment_level_iou, frame_accuracy, 
    action_set_coverage, lev_norm, compute_metrics
)

# 设置可视化风格
sns.set_theme(style="whitegrid")

# 全局配置
DATA_ROOT = "/data2/syd_data/Breakfast_Data"
LABEL_MAP_PATH = os.path.join(DATA_ROOT, "label_map.json")
TEST_DATA_DIR = os.path.join(DATA_ROOT, "breakfast_data_npy")
TEST_LABEL_DIR = os.path.join(DATA_ROOT, "segmentation_coarse_npy")

# 输入路径配置（从训练步骤的输出）
STATIC_MODEL_DIR = os.path.join(DATA_ROOT, "Outputs", "Static", "Model_parameters")
EDGE_WEIGHT_DIR = os.path.join(DATA_ROOT, "Outputs", "Edge_Weight", "Model_parameters")

# 输出路径配置（符合IDEA.md第5.4章规范）
OUTPUT_BASE = os.path.join(DATA_ROOT, "Outputs", "Evaluation_Result")

# 生成时间戳ID
TIMESTAMP_ID = datetime.now().strftime("%Y%m%d-%H%M%S")


class StaticModel(torch.nn.Module):
    """静态任务图模型（用于推理）"""
    
    def __init__(self, edge_weights: torch.Tensor, bias: torch.Tensor):
        super().__init__()
        self.register_buffer('edge_weights', edge_weights)
        self.register_buffer('bias', bias)
    
    def forward(self, current_actions: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        return self.edge_weights[current_actions] + self.bias


class StaticModelTester:
    """静态模型测试器"""
    
    def __init__(self):
        self.label_map = self.load_label_map()
        self.M = len(self.label_map)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建输出目录
        os.makedirs(OUTPUT_BASE, exist_ok=True)
        
        print(f"🧪 静态模型测试器初始化")
        print(f"   - 动作类别数: {self.M}")
        print(f"   - 设备: {self.device}")
        print(f"   - 时间戳ID: {TIMESTAMP_ID}")
    
    def load_label_map(self) -> Dict[str, str]:
        """加载标签映射"""
        with open(LABEL_MAP_PATH, 'r', encoding='utf-8') as f:
            label_map = json.load(f)
        return label_map
    
    def load_static_model(self) -> StaticModel:
        """加载训练好的静态模型"""
        # 查找最新的静态模型文件
        static_files = [f for f in os.listdir(STATIC_MODEL_DIR) if f.startswith('static_model_full_') and f.endswith('.pt')]
        
        if not static_files:
            raise FileNotFoundError(f"未找到静态模型文件在 {STATIC_MODEL_DIR}")
        
        latest_file = sorted(static_files)[-1]
        model_path = os.path.join(STATIC_MODEL_DIR, latest_file)
        
        # 加载模型数据
        model_data = torch.load(model_path, map_location='cpu')
        edge_weights = model_data['edge_weights']
        bias = model_data['bias']
        
        print(f"📥 加载静态模型: {model_path}")
        print(f"   - 边权重形状: {edge_weights.shape}")
        print(f"   - 偏置形状: {bias.shape}")
        
        # 创建模型
        model = StaticModel(edge_weights, bias).to(self.device)
        model.eval()
        
        return model
    
    def find_test_files(self) -> List[Tuple[str, str]]:
        """查找测试文件对（特征文件，标签文件）"""
        file_pairs = []
        
        # 扫描s4的测试数据
        subject = "s4"
        subject_feat_dir = os.path.join(TEST_DATA_DIR, subject)
        subject_label_dir = os.path.join(TEST_LABEL_DIR, f"{subject}_label")
        
        if not os.path.exists(subject_feat_dir) or not os.path.exists(subject_label_dir):
            raise FileNotFoundError(f"测试数据目录不存在: {subject_feat_dir} 或 {subject_label_dir}")
        
        # 遍历任务目录
        for task_dir in os.listdir(subject_feat_dir):
            feat_task_path = os.path.join(subject_feat_dir, task_dir)
            label_task_path = os.path.join(subject_label_dir, task_dir)
            
            if not os.path.isdir(feat_task_path) or not os.path.isdir(label_task_path):
                continue
            
            # 匹配特征文件和标签文件
            for feat_file in os.listdir(feat_task_path):
                if feat_file.endswith('.npy'):
                    feat_path = os.path.join(feat_task_path, feat_file)
                    label_file = feat_file  # 假设文件名相同
                    label_path = os.path.join(label_task_path, label_file)
                    
                    if os.path.exists(label_path):
                        file_pairs.append((feat_path, label_path))
        
        print(f"📁 找到 {len(file_pairs)} 个测试文件对")
        return file_pairs
    
    def predict_sequence(self, model: StaticModel, features: torch.Tensor, 
                        initial_action: int) -> List[int]:
        """
        对单个序列进行预测
        
        Args:
            model: 静态模型
            features: 特征序列 (N, D)
            initial_action: 初始动作（第一帧的真实标签）
            
        Returns:
            预测的动作序列
        """
        model.eval()
        predictions = [initial_action]
        current_action = initial_action
        
        with torch.no_grad():
            for k in range(len(features) - 1):
                # 使用当前动作预测下一动作
                current_action_tensor = torch.tensor([current_action], device=self.device)
                logits = model(current_action_tensor)
                
                # 预测下一动作
                next_action = torch.argmax(logits, dim=1).item()
                predictions.append(next_action)
                
                # 更新当前动作为预测值（非Teacher Forcing）
                current_action = next_action
        
        return predictions
    
    def run_inference(self, model: StaticModel, file_pairs: List[Tuple[str, str]]) -> Tuple[List[List[int]], List[List[int]]]:
        """
        在所有测试文件上运行推理
        
        Returns:
            (predictions, ground_truths): 预测序列列表和真实序列列表
        """
        print("🔄 开始推理测试...")
        
        all_predictions = []
        all_ground_truths = []
        
        for i, (feat_path, label_path) in enumerate(file_pairs):
            try:
                # 加载数据
                features = torch.from_numpy(np.load(feat_path)).float().to(self.device)
                labels = np.load(label_path)
                
                # 确保维度匹配
                min_len = min(len(features), len(labels))
                features = features[:min_len]
                labels = labels[:min_len]
                
                if len(features) < 2:
                    print(f"⚠️ 跳过过短序列: {feat_path}")
                    continue
                
                # 进行预测
                initial_action = int(labels[0])
                predictions = self.predict_sequence(model, features, initial_action)
                
                # 确保预测和真实序列长度一致
                min_pred_len = min(len(predictions), len(labels))
                predictions = predictions[:min_pred_len]
                ground_truth = labels[:min_pred_len].tolist()
                
                all_predictions.append(predictions)
                all_ground_truths.append(ground_truth)
                
                if (i + 1) % 10 == 0:
                    print(f"   处理进度: {i + 1}/{len(file_pairs)}")
                
            except Exception as e:
                print(f"⚠️ 处理文件失败 {feat_path}: {e}")
                continue
        
        print(f"✅ 推理完成: {len(all_predictions)} 个序列")
        return all_predictions, all_ground_truths

    def compute_evaluation_metrics(self, predictions: List[List[int]],
                                 ground_truths: List[List[int]]) -> Dict:
        """计算评估指标"""
        print("📊 计算评估指标...")

        # 使用现有的评估指标函数
        metrics = compute_metrics(predictions, ground_truths)

        # 添加更详细的统计信息
        detailed_metrics = {
            'segment_iou_mean': metrics['segment_iou_mean'],
            'frame_accuracy_mean': metrics['frame_accuracy_mean'],
            'action_coverage_mean': metrics['action_coverage_mean'],
            'lev_mean': metrics['lev_mean'],
            'num_sequences': len(predictions),
            'total_frames': sum(len(pred) for pred in predictions),
            'avg_sequence_length': sum(len(pred) for pred in predictions) / len(predictions) if predictions else 0
        }

        print(f"   ✅ 评估指标计算完成:")
        print(f"      - 时序段级IoU: {detailed_metrics['segment_iou_mean']:.4f}")
        print(f"      - 帧级准确度: {detailed_metrics['frame_accuracy_mean']:.4f}")
        print(f"      - 动作覆盖率: {detailed_metrics['action_coverage_mean']:.4f}")
        print(f"      - 归一化编辑距离: {detailed_metrics['lev_mean']:.4f}")

        return detailed_metrics

    def save_results(self, predictions: List[List[int]], ground_truths: List[List[int]],
                    metrics: Dict):
        """保存测试结果"""
        print("💾 保存测试结果...")

        # 1. 保存原始预测序列
        predictions_path = os.path.join(OUTPUT_BASE, f"static_predictions_{TIMESTAMP_ID}.json")
        with open(predictions_path, 'w', encoding='utf-8') as f:
            json.dump({
                'predictions': predictions,
                'ground_truths': ground_truths,
                'timestamp_id': TIMESTAMP_ID,
                'model_type': 'static',
                'label_map': self.label_map
            }, f, indent=2)

        # 2. 保存评估指标
        metrics_path = os.path.join(OUTPUT_BASE, f"static_metrics_{TIMESTAMP_ID}.json")
        with open(metrics_path, 'w', encoding='utf-8') as f:
            json.dump(metrics, f, indent=2)

        # 3. 保存CSV格式的评估结果
        csv_path = os.path.join(OUTPUT_BASE, f"static_evaluation_{TIMESTAMP_ID}.csv")
        with open(csv_path, 'w', encoding='utf-8') as f:
            f.write("model_name,metric_name,point_estimate\n")
            f.write(f"Static,Segment_IoU,{metrics['segment_iou_mean']:.6f}\n")
            f.write(f"Static,Frame_Accuracy,{metrics['frame_accuracy_mean']:.6f}\n")
            f.write(f"Static,Action_Coverage,{metrics['action_coverage_mean']:.6f}\n")
            f.write(f"Static,Normalized_Edit_Distance,{metrics['lev_mean']:.6f}\n")

        print(f"   ✅ 预测序列已保存: {predictions_path}")
        print(f"   ✅ 评估指标已保存: {metrics_path}")
        print(f"   ✅ CSV结果已保存: {csv_path}")

    def generate_visualizations(self, predictions: List[List[int]],
                              ground_truths: List[List[int]], metrics: Dict):
        """生成可视化图表"""
        print("📊 生成可视化图表...")

        # 1. 评估指标柱状图
        plt.figure(figsize=(10, 6))

        metric_names = ['Segment IoU', 'Frame Accuracy', 'Action Coverage', 'Norm Edit Dist']
        metric_values = [
            metrics['segment_iou_mean'],
            metrics['frame_accuracy_mean'],
            metrics['action_coverage_mean'],
            metrics['lev_mean']
        ]

        bars = plt.bar(metric_names, metric_values, color=['skyblue', 'lightgreen', 'lightcoral', 'gold'])
        plt.ylabel('分数')
        plt.title('静态模型评估指标')
        plt.ylim(0, 1)

        # 在柱状图上添加数值标签
        for bar, value in zip(bars, metric_values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{value:.3f}', ha='center', va='bottom')

        plt.tight_layout()
        metrics_plot_path = os.path.join(OUTPUT_BASE, f"static_metrics_plot_{TIMESTAMP_ID}.png")
        plt.savefig(metrics_plot_path, dpi=300, bbox_inches='tight')
        plt.close()

        # 2. 序列长度分布图
        plt.figure(figsize=(10, 6))

        pred_lengths = [len(pred) for pred in predictions]
        gt_lengths = [len(gt) for gt in ground_truths]

        plt.hist(pred_lengths, bins=20, alpha=0.7, label='预测序列长度', color='blue')
        plt.hist(gt_lengths, bins=20, alpha=0.7, label='真实序列长度', color='red')
        plt.xlabel('序列长度')
        plt.ylabel('频次')
        plt.title('序列长度分布')
        plt.legend()
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        length_dist_path = os.path.join(OUTPUT_BASE, f"static_length_distribution_{TIMESTAMP_ID}.png")
        plt.savefig(length_dist_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"   ✅ 可视化图表已保存到: {OUTPUT_BASE}")

    def run_testing(self):
        """执行完整的测试流程"""
        print("=" * 80)
        print("🧪 静态模型测试开始")
        print("=" * 80)

        # 1. 加载模型
        model = self.load_static_model()

        # 2. 查找测试文件
        file_pairs = self.find_test_files()

        # 3. 运行推理
        predictions, ground_truths = self.run_inference(model, file_pairs)

        if not predictions:
            print("❌ 没有成功的预测结果!")
            return

        # 4. 计算评估指标
        metrics = self.compute_evaluation_metrics(predictions, ground_truths)

        # 5. 保存结果
        self.save_results(predictions, ground_truths, metrics)

        # 6. 生成可视化
        self.generate_visualizations(predictions, ground_truths, metrics)

        print("=" * 80)
        print("✅ 静态模型测试完成!")
        print(f"📁 输出目录: {OUTPUT_BASE}")
        print(f"🏷️ 时间戳ID: {TIMESTAMP_ID}")
        print("=" * 80)


def main():
    """主函数"""
    tester = StaticModelTester()
    tester.run_testing()


if __name__ == "__main__":
    main()
