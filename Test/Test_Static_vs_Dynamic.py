#!/usr/bin/env python3
"""
静态vs动态模型对比测试脚本 - Test_Static_vs_Dynamic.py
根据IDEA.md第2.5节规范实现

主要功能：
1. 加载静态和动态模型的测试结果
2. 对每个指标做bootstrap 500次(按视频级重抽)
3. 报告均值±95% CI
4. 写入evaluation_results/bootstrap_ci.csv
5. 生成详细的对比分析报告和可视化图表
"""

import os
import sys
import numpy as np
import pandas as pd
import json
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
from scipy import stats

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入评估指标
from src.utils.metrics import (
    greedy_merge, segment_level_iou, frame_accuracy, 
    action_set_coverage, lev_norm, compute_metrics
)

# 设置可视化风格
sns.set_theme(style="whitegrid")

# 全局配置
DATA_ROOT = "/data2/syd_data/Breakfast_Data"
LABEL_MAP_PATH = os.path.join(DATA_ROOT, "label_map.json")

# 输入路径配置（从测试步骤的输出）
EVALUATION_DIR = os.path.join(DATA_ROOT, "Outputs", "Evaluation_Result")

# 输出路径配置
OUTPUT_BASE = os.path.join(DATA_ROOT, "Outputs", "Evaluation_Result")

# 生成时间戳ID
TIMESTAMP_ID = datetime.now().strftime("%Y%m%d-%H%M%S")

# Bootstrap参数
N_BOOTSTRAP = 500
CONFIDENCE_LEVEL = 0.95
RANDOM_SEED = 42


class BootstrapEvaluator:
    """Bootstrap置信区间评估器"""
    
    def __init__(self):
        self.label_map = self.load_label_map()
        self.rng = np.random.default_rng(RANDOM_SEED)
        
        print(f"📊 Bootstrap评估器初始化")
        print(f"   - Bootstrap次数: {N_BOOTSTRAP}")
        print(f"   - 置信水平: {CONFIDENCE_LEVEL}")
        print(f"   - 时间戳ID: {TIMESTAMP_ID}")
    
    def load_label_map(self) -> Dict[str, str]:
        """加载标签映射"""
        with open(LABEL_MAP_PATH, 'r', encoding='utf-8') as f:
            label_map = json.load(f)
        return label_map
    
    def find_latest_results(self) -> Tuple[Optional[str], Optional[str]]:
        """查找最新的静态和动态测试结果文件"""
        static_files = [f for f in os.listdir(EVALUATION_DIR) if f.startswith('static_predictions_') and f.endswith('.json')]
        dynamic_files = [f for f in os.listdir(EVALUATION_DIR) if f.startswith('dynamic_predictions_') and f.endswith('.json')]
        
        static_path = None
        dynamic_path = None
        
        if static_files:
            latest_static = sorted(static_files)[-1]
            static_path = os.path.join(EVALUATION_DIR, latest_static)
            print(f"📥 找到静态结果: {latest_static}")
        
        if dynamic_files:
            latest_dynamic = sorted(dynamic_files)[-1]
            dynamic_path = os.path.join(EVALUATION_DIR, latest_dynamic)
            print(f"📥 找到动态结果: {latest_dynamic}")
        
        return static_path, dynamic_path
    
    def load_predictions(self, file_path: str) -> Tuple[List[List[int]], List[List[int]]]:
        """加载预测结果"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        predictions = data['predictions']
        ground_truths = data['ground_truths']
        
        print(f"   - 加载了 {len(predictions)} 个序列")
        return predictions, ground_truths
    
    def bootstrap_metric(self, predictions: List[List[int]], ground_truths: List[List[int]], 
                        metric_func, n_bootstrap: int = N_BOOTSTRAP) -> Tuple[float, float, float]:
        """
        对单个指标进行bootstrap采样
        
        Args:
            predictions: 预测序列列表
            ground_truths: 真实序列列表
            metric_func: 指标计算函数
            n_bootstrap: bootstrap次数
            
        Returns:
            (mean, lower_ci, upper_ci): 均值和95%置信区间
        """
        N = len(predictions)
        bootstrap_scores = []
        
        for _ in range(n_bootstrap):
            # 按视频级重采样
            sample_indices = self.rng.choice(N, N, replace=True)
            sample_preds = [predictions[i] for i in sample_indices]
            sample_gts = [ground_truths[i] for i in sample_indices]
            
            # 计算指标
            score = metric_func(sample_preds, sample_gts)
            bootstrap_scores.append(score)
        
        # 计算置信区间
        mean_score = np.mean(bootstrap_scores)
        alpha = 1 - CONFIDENCE_LEVEL
        lower_percentile = (alpha / 2) * 100
        upper_percentile = (1 - alpha / 2) * 100
        
        lower_ci = np.percentile(bootstrap_scores, lower_percentile)
        upper_ci = np.percentile(bootstrap_scores, upper_percentile)
        
        return mean_score, lower_ci, upper_ci
    
    def compute_all_bootstrap_metrics(self, predictions: List[List[int]], 
                                    ground_truths: List[List[int]], model_name: str) -> Dict:
        """计算所有指标的bootstrap置信区间"""
        print(f"🔄 计算 {model_name} 的Bootstrap置信区间...")
        
        # 定义指标函数
        def segment_iou_metric(preds, gts):
            metrics = compute_metrics(preds, gts)
            return metrics['segment_iou_mean']
        
        def frame_accuracy_metric(preds, gts):
            metrics = compute_metrics(preds, gts)
            return metrics['frame_accuracy_mean']
        
        def action_coverage_metric(preds, gts):
            metrics = compute_metrics(preds, gts)
            return metrics['action_coverage_mean']
        
        def lev_metric(preds, gts):
            metrics = compute_metrics(preds, gts)
            return metrics['lev_mean']
        
        # 计算各指标的bootstrap置信区间
        metrics_results = {}
        
        # Segment IoU
        mean, lower, upper = self.bootstrap_metric(predictions, ground_truths, segment_iou_metric)
        metrics_results['Segment_IoU'] = {
            'mean': mean,
            'ci_lower': lower,
            'ci_upper': upper
        }
        
        # Frame Accuracy
        mean, lower, upper = self.bootstrap_metric(predictions, ground_truths, frame_accuracy_metric)
        metrics_results['Frame_Accuracy'] = {
            'mean': mean,
            'ci_lower': lower,
            'ci_upper': upper
        }
        
        # Action Coverage
        mean, lower, upper = self.bootstrap_metric(predictions, ground_truths, action_coverage_metric)
        metrics_results['Action_Coverage'] = {
            'mean': mean,
            'ci_lower': lower,
            'ci_upper': upper
        }
        
        # Normalized Edit Distance
        mean, lower, upper = self.bootstrap_metric(predictions, ground_truths, lev_metric)
        metrics_results['Normalized_Edit_Distance'] = {
            'mean': mean,
            'ci_lower': lower,
            'ci_upper': upper
        }
        
        print(f"   ✅ {model_name} Bootstrap计算完成")
        for metric_name, result in metrics_results.items():
            print(f"      {metric_name}: {result['mean']:.4f} [{result['ci_lower']:.4f}, {result['ci_upper']:.4f}]")
        
        return metrics_results
    
    def save_bootstrap_results(self, static_results: Dict, dynamic_results: Dict):
        """保存bootstrap结果到CSV"""
        print("💾 保存Bootstrap结果...")
        
        # 创建CSV数据
        csv_data = []
        
        for metric_name in static_results.keys():
            # 静态模型结果
            static_result = static_results[metric_name]
            csv_data.append({
                'model_name': 'Static',
                'metric_name': metric_name,
                'point_estimate': static_result['mean'],
                'ci_lower_bound': static_result['ci_lower'],
                'ci_upper_bound': static_result['ci_upper']
            })
            
            # 动态模型结果
            dynamic_result = dynamic_results[metric_name]
            csv_data.append({
                'model_name': 'Dynamic',
                'metric_name': metric_name,
                'point_estimate': dynamic_result['mean'],
                'ci_lower_bound': dynamic_result['ci_lower'],
                'ci_upper_bound': dynamic_result['ci_upper']
            })
        
        # 保存为CSV
        df = pd.DataFrame(csv_data)
        csv_path = os.path.join(OUTPUT_BASE, f"bootstrap_ci_{TIMESTAMP_ID}.csv")
        df.to_csv(csv_path, index=False)
        
        # 保存为JSON（更详细的信息）
        json_data = {
            'static_results': static_results,
            'dynamic_results': dynamic_results,
            'metadata': {
                'timestamp_id': TIMESTAMP_ID,
                'n_bootstrap': N_BOOTSTRAP,
                'confidence_level': CONFIDENCE_LEVEL,
                'random_seed': RANDOM_SEED
            }
        }
        
        json_path = os.path.join(OUTPUT_BASE, f"bootstrap_detailed_{TIMESTAMP_ID}.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2)
        
        print(f"   ✅ Bootstrap结果已保存: {csv_path}")
        print(f"   ✅ 详细结果已保存: {json_path}")
        
        return csv_path, json_path

    def generate_comparison_visualizations(self, static_results: Dict, dynamic_results: Dict):
        """生成对比可视化图表"""
        print("📊 生成对比可视化图表...")

        # 1. 置信区间对比图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.flatten()

        metric_names = list(static_results.keys())
        colors = ['skyblue', 'lightgreen', 'lightcoral', 'gold']

        for i, metric_name in enumerate(metric_names):
            ax = axes[i]

            static_result = static_results[metric_name]
            dynamic_result = dynamic_results[metric_name]

            # 绘制置信区间
            models = ['Static', 'Dynamic']
            means = [static_result['mean'], dynamic_result['mean']]
            ci_lowers = [static_result['ci_lower'], dynamic_result['ci_lower']]
            ci_uppers = [static_result['ci_upper'], dynamic_result['ci_upper']]

            # 计算误差条
            yerr_lower = [means[j] - ci_lowers[j] for j in range(2)]
            yerr_upper = [ci_uppers[j] - means[j] for j in range(2)]

            bars = ax.bar(models, means, color=[colors[i], colors[i]], alpha=0.7)
            ax.errorbar(models, means, yerr=[yerr_lower, yerr_upper],
                       fmt='none', color='black', capsize=5, capthick=2)

            # 添加数值标签
            for j, (bar, mean, ci_lower, ci_upper) in enumerate(zip(bars, means, ci_lowers, ci_uppers)):
                ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + yerr_upper[j] + 0.01,
                       f'{mean:.3f}\n[{ci_lower:.3f}, {ci_upper:.3f}]',
                       ha='center', va='bottom', fontsize=9)

            ax.set_title(f'{metric_name.replace("_", " ")}')
            ax.set_ylabel('分数')
            ax.set_ylim(0, 1.1)
            ax.grid(True, alpha=0.3)

        plt.tight_layout()
        comparison_plot_path = os.path.join(OUTPUT_BASE, f"static_vs_dynamic_comparison_{TIMESTAMP_ID}.png")
        plt.savefig(comparison_plot_path, dpi=300, bbox_inches='tight')
        plt.close()

        # 2. 改进幅度图
        plt.figure(figsize=(10, 6))

        improvements = []
        metric_labels = []

        for metric_name in metric_names:
            static_mean = static_results[metric_name]['mean']
            dynamic_mean = dynamic_results[metric_name]['mean']

            # 计算相对改进（对于编辑距离，改进是减少）
            if 'Edit_Distance' in metric_name:
                improvement = (static_mean - dynamic_mean) / static_mean * 100
            else:
                improvement = (dynamic_mean - static_mean) / static_mean * 100

            improvements.append(improvement)
            metric_labels.append(metric_name.replace('_', ' '))

        # 绘制改进幅度
        colors_improvement = ['green' if imp > 0 else 'red' for imp in improvements]
        bars = plt.bar(metric_labels, improvements, color=colors_improvement, alpha=0.7)

        # 添加数值标签
        for bar, improvement in zip(bars, improvements):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2, height + (0.5 if height > 0 else -0.5),
                    f'{improvement:+.1f}%', ha='center', va='bottom' if height > 0 else 'top')

        plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        plt.ylabel('相对改进 (%)')
        plt.title('动态模型相对于静态模型的改进')
        plt.xticks(rotation=45, ha='right')
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        improvement_plot_path = os.path.join(OUTPUT_BASE, f"improvement_analysis_{TIMESTAMP_ID}.png")
        plt.savefig(improvement_plot_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"   ✅ 对比可视化已保存到: {OUTPUT_BASE}")

        return comparison_plot_path, improvement_plot_path

    def perform_statistical_tests(self, static_results: Dict, dynamic_results: Dict) -> Dict:
        """执行统计显著性检验"""
        print("📈 执行统计显著性检验...")

        # 注意：这里我们只能基于置信区间进行简单的显著性判断
        # 真正的统计检验需要原始的bootstrap样本

        significance_results = {}

        for metric_name in static_results.keys():
            static_result = static_results[metric_name]
            dynamic_result = dynamic_results[metric_name]

            # 检查置信区间是否重叠
            static_ci = (static_result['ci_lower'], static_result['ci_upper'])
            dynamic_ci = (dynamic_result['ci_lower'], dynamic_result['ci_upper'])

            # 简单的重叠检验
            overlap = not (static_ci[1] < dynamic_ci[0] or dynamic_ci[1] < static_ci[0])

            # 计算效应大小（Cohen's d的近似）
            static_mean = static_result['mean']
            dynamic_mean = dynamic_result['mean']

            # 使用置信区间估计标准差
            static_std = (static_result['ci_upper'] - static_result['ci_lower']) / (2 * 1.96)
            dynamic_std = (dynamic_result['ci_upper'] - dynamic_result['ci_lower']) / (2 * 1.96)
            pooled_std = np.sqrt((static_std**2 + dynamic_std**2) / 2)

            cohens_d = (dynamic_mean - static_mean) / pooled_std if pooled_std > 0 else 0

            significance_results[metric_name] = {
                'ci_overlap': overlap,
                'likely_significant': not overlap,
                'cohens_d': cohens_d,
                'effect_size': 'small' if abs(cohens_d) < 0.5 else 'medium' if abs(cohens_d) < 0.8 else 'large'
            }

        print("   ✅ 统计检验完成")
        return significance_results

    def run_comparison(self):
        """执行完整的对比分析"""
        print("=" * 80)
        print("📊 静态vs动态模型对比分析开始")
        print("=" * 80)

        # 1. 查找最新结果文件
        static_path, dynamic_path = self.find_latest_results()

        if not static_path or not dynamic_path:
            print("❌ 未找到完整的测试结果文件!")
            print(f"   静态结果: {'✅' if static_path else '❌'}")
            print(f"   动态结果: {'✅' if dynamic_path else '❌'}")
            return

        # 2. 加载预测结果
        print("\n📥 加载预测结果...")
        static_preds, static_gts = self.load_predictions(static_path)
        dynamic_preds, dynamic_gts = self.load_predictions(dynamic_path)

        # 3. 计算Bootstrap置信区间
        print("\n🔄 计算Bootstrap置信区间...")
        static_results = self.compute_all_bootstrap_metrics(static_preds, static_gts, "Static")
        dynamic_results = self.compute_all_bootstrap_metrics(dynamic_preds, dynamic_gts, "Dynamic")

        # 4. 保存结果
        print("\n💾 保存对比结果...")
        csv_path, json_path = self.save_bootstrap_results(static_results, dynamic_results)

        # 5. 生成可视化
        print("\n📊 生成对比可视化...")
        comparison_plot, improvement_plot = self.generate_comparison_visualizations(static_results, dynamic_results)

        # 6. 统计显著性检验
        print("\n📈 统计显著性分析...")
        significance_results = self.perform_statistical_tests(static_results, dynamic_results)

        # 7. 生成总结报告
        print("\n📋 生成总结报告...")
        self.generate_summary_report(static_results, dynamic_results, significance_results)

        print("=" * 80)
        print("✅ 静态vs动态模型对比分析完成!")
        print(f"📁 输出目录: {OUTPUT_BASE}")
        print(f"🏷️ 时间戳ID: {TIMESTAMP_ID}")
        print("=" * 80)

    def generate_summary_report(self, static_results: Dict, dynamic_results: Dict,
                              significance_results: Dict):
        """生成总结报告"""
        report_path = os.path.join(OUTPUT_BASE, f"comparison_summary_{TIMESTAMP_ID}.txt")

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("静态vs动态任务图模型对比分析报告\n")
            f.write("=" * 80 + "\n\n")

            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Bootstrap次数: {N_BOOTSTRAP}\n")
            f.write(f"置信水平: {CONFIDENCE_LEVEL}\n\n")

            f.write("详细结果 (均值 ± 95% CI):\n")
            f.write("-" * 50 + "\n")

            for metric_name in static_results.keys():
                static_result = static_results[metric_name]
                dynamic_result = dynamic_results[metric_name]
                sig_result = significance_results[metric_name]

                f.write(f"\n{metric_name.replace('_', ' ')}:\n")
                f.write(f"  静态模型: {static_result['mean']:.4f} [{static_result['ci_lower']:.4f}, {static_result['ci_upper']:.4f}]\n")
                f.write(f"  动态模型: {dynamic_result['mean']:.4f} [{dynamic_result['ci_lower']:.4f}, {dynamic_result['ci_upper']:.4f}]\n")
                f.write(f"  效应大小: {sig_result['cohens_d']:.3f} ({sig_result['effect_size']})\n")
                f.write(f"  可能显著: {'是' if sig_result['likely_significant'] else '否'}\n")

        print(f"   ✅ 总结报告已保存: {report_path}")


def main():
    """主函数"""
    evaluator = BootstrapEvaluator()
    evaluator.run_comparison()


if __name__ == "__main__":
    main()
