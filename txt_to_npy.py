#!/usr/bin/env python3
"""
TXT到NPY格式转换脚本 - txt_to_npy.py
根据IDEA.md第1章规范实现

主要功能：
1. 每个 .txt 文件单独转换为 .npy
2. 保留第1列帧索引，形状为 (N, 65)
3. 不做多视角融合（融合逻辑由数据加载器在训练时完成）
4. 数值原样拷贝，不做任何归一化或截断
5. 转换标签文件
"""

import os
import sys
import numpy as np
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional

class TxtToNpyConverter:
    """TXT到NPY转换器 - 简单单文件转换"""
    
    def __init__(self):
        # 路径配置
        self.raw_data_path = Path("/data2/syd_data/Breakfast_Data/breakfast_data")
        self.raw_label_path = Path("/data2/syd_data/Breakfast_Data/segmentation_coarse")
        self.npy_data_path = Path("/data2/syd_data/Breakfast_Data/breakfast_data_npy")
        self.npy_label_path = Path("/data2/syd_data/Breakfast_Data/segmentation_coarse_npy")
        self.label_map_path = Path("/data2/syd_data/Breakfast_Data/label_map.json")
        
        # 设置日志
        self.setup_logging()
        
        # 转换统计
        self.stats = {
            'total_files': 0,
            'converted_files': 0,
            'failed_files': 0,
            'total_labels': 0,
            'converted_labels': 0,
            'failed_labels': 0
        }
        
        # 标签映射
        self.label_map = {}
        self.label_to_id = {}
        
    def setup_logging(self):
        """设置日志"""
        log_path = Path("/data2/syd_data/Breakfast_Data/Code/txt_to_npy.log")
        
        logging.basicConfig(
            filename=log_path,
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            filemode='w'
        )
        
        # 同时输出到控制台
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        logging.getLogger().addHandler(console_handler)
        
        self.logger = logging.getLogger(__name__)
        
    def convert_single_feature_file(self, txt_path: Path, npy_path: Path) -> bool:
        """转换单个特征文件"""
        try:
            # 加载txt文件
            data = np.loadtxt(txt_path)
            if data.ndim == 1:
                data = data.reshape(1, -1)
            
            # 检查列数（应该是65列：1列帧索引 + 64列特征）
            if data.shape[1] != 65:
                self.logger.warning(f"Column count mismatch for {txt_path.name}: {data.shape[1]} columns, expected 65")
            
            # 第一列转为整型（帧索引）
            data_with_int_idx = np.column_stack([
                data[:, 0].astype(np.int32),  # 帧索引转为整型
                data[:, 1:]  # 特征保持浮点
            ])
            
            # 验证转换
            if not self.validate_conversion(data, data_with_int_idx):
                return False
            
            # 保存为npy
            np.save(npy_path, data_with_int_idx)
            
            self.logger.debug(f"Converted: {txt_path.name} -> {npy_path.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to convert {txt_path.name}: {e}")
            return False
            
    def validate_conversion(self, original: np.ndarray, converted: np.ndarray) -> bool:
        """验证转换的数值一致性"""
        try:
            # 检查行数
            if original.shape[0] != converted.shape[0]:
                self.logger.error(f"Row count mismatch: {original.shape[0]} vs {converted.shape[0]}")
                return False
            
            # 检查数值误差（第一列是整型转换，允许舍入误差；其他列要求精确）
            # 第一列：帧索引
            frame_idx_diff = np.abs(original[:, 0] - converted[:, 0])
            if np.max(frame_idx_diff) > 1.0:  # 整型转换允许最多1.0的误差
                self.logger.error(f"Frame index conversion error: max diff = {np.max(frame_idx_diff)}")
                return False
            
            # 其他列：特征值
            if original.shape[1] > 1 and converted.shape[1] > 1:
                feature_diff = np.abs(original[:, 1:] - converted[:, 1:])
                max_diff = np.max(feature_diff)
                if max_diff > 1e-6:
                    self.logger.error(f"Feature value mismatch: max diff = {max_diff}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Validation error: {e}")
            return False
            
    def convert_features(self):
        """转换所有特征文件"""
        self.logger.info("Starting feature conversion...")
        
        # 遍历s1-s4
        for split in ['s1', 's2', 's3', 's4']:
            split_dir = self.raw_data_path / split
            if not split_dir.exists():
                self.logger.warning(f"Split directory not found: {split_dir}")
                continue
                
            # 创建输出目录
            output_split_dir = self.npy_data_path / split
            output_split_dir.mkdir(parents=True, exist_ok=True)
            
            # 遍历每个任务
            for task_dir in split_dir.iterdir():
                if not task_dir.is_dir():
                    continue
                    
                task_name = task_dir.name
                self.logger.info(f"Processing {split}/{task_name}")
                
                # 创建任务输出目录
                output_task_dir = output_split_dir / task_name
                output_task_dir.mkdir(parents=True, exist_ok=True)
                
                # 获取所有txt文件
                txt_files = list(task_dir.glob("*.txt"))
                if not txt_files:
                    self.logger.warning(f"No txt files found in {task_dir}")
                    continue
                
                # 处理每个txt文件
                for txt_file in txt_files:
                    self.stats['total_files'] += 1
                    
                    # 输出文件路径
                    output_filename = txt_file.stem + ".npy"
                    output_path = output_task_dir / output_filename
                    
                    # 转换文件
                    if self.convert_single_feature_file(txt_file, output_path):
                        self.stats['converted_files'] += 1
                    else:
                        self.stats['failed_files'] += 1
                        
        self.logger.info(f"Feature conversion completed: {self.stats['converted_files']}/{self.stats['total_files']} files")
        
    def parse_label_line(self, line: str) -> Tuple[int, int, str]:
        """解析标签行"""
        parts = line.strip().split()
        if len(parts) >= 2:
            frame_range = parts[0]
            action = ' '.join(parts[1:])
            
            if '-' in frame_range:
                start, end = frame_range.split('-')
                return int(start), int(end), action
                
        raise ValueError(f"Invalid label line: {line}")
        
    def convert_labels(self):
        """转换所有标签文件"""
        self.logger.info("Starting label conversion...")
        
        # 加载已有的标签映射，如果不存在则创建新的
        if self.label_map_path.exists():
            self.logger.info(f"Loading existing label map from {self.label_map_path}")
            with open(self.label_map_path, 'r', encoding='utf-8') as f:
                label_data = json.load(f)
            # 转换为正确的格式
            self.label_map = {int(k): v for k, v in label_data.items()}
            self.label_to_id = {v: int(k) for k, v in label_data.items()}
            self.logger.info(f"Loaded label map with {len(self.label_map)} actions")
        else:
            # 如果不存在，收集所有动作类型并创建映射
            self.logger.info("Label map not found, creating new one...")
            all_actions = set()
            
            # 遍历s1-s4
            for split in ['s1', 's2', 's3', 's4']:
                label_dir = self.raw_label_path / f"{split}_label"
                if not label_dir.exists():
                    self.logger.warning(f"Label directory not found: {label_dir}")
                    continue
                    
                # 遍历所有任务子目录中的标签文件
                for task_dir in label_dir.iterdir():
                    if not task_dir.is_dir():
                        continue
                        
                    for label_file in task_dir.glob("*.txt"):
                        with open(label_file, 'r', encoding='utf-8') as f:
                            for line in f:
                                line = line.strip()
                                if line:
                                    try:
                                        _, _, action = self.parse_label_line(line)
                                        all_actions.add(action)
                                    except:
                                        pass
                                    
            # 创建标签映射
            sorted_actions = sorted(all_actions)
            self.label_map = {i: action for i, action in enumerate(sorted_actions)}
            self.label_to_id = {action: i for i, action in enumerate(sorted_actions)}
            
            # 保存标签映射
            with open(self.label_map_path, 'w', encoding='utf-8') as f:
                json.dump(self.label_map, f, ensure_ascii=False, indent=2)
            self.logger.info(f"Created and saved label map with {len(self.label_map)} actions")
        
        # 转换标签文件
        for split in ['s1', 's2', 's3', 's4']:
            label_dir = self.raw_label_path / f"{split}_label"
            if not label_dir.exists():
                continue
                
            # 遍历每个任务子目录
            for task_dir in label_dir.iterdir():
                if not task_dir.is_dir():
                    continue
                    
                task_name = task_dir.name
                
                # 创建输出目录
                output_dir = self.npy_label_path / f"{split}_label" / task_name
                output_dir.mkdir(parents=True, exist_ok=True)
                
                # 处理任务目录中的每个标签文件
                for label_file in task_dir.glob("*.txt"):
                    self.stats['total_labels'] += 1
                    
                    try:
                        # 解析标签
                        segments = []
                        max_frame = 0
                        
                        with open(label_file, 'r', encoding='utf-8') as f:
                            for line in f:
                                line = line.strip()
                                if line:
                                    start, end, action = self.parse_label_line(line)
                                    action_id = self.label_to_id.get(action, -1)
                                    segments.append((start, end, action_id))
                                    max_frame = max(max_frame, end)
                                    
                        # 创建帧级标签
                        frame_labels = np.zeros(max_frame + 1, dtype=np.int32)
                        for start, end, action_id in segments:
                            frame_labels[start:end+1] = action_id
                            
                        # 保存标签
                        output_filename = label_file.stem + ".npy"
                        output_path = output_dir / output_filename
                        np.save(output_path, frame_labels)
                        
                        self.stats['converted_labels'] += 1
                        self.logger.debug(f"Converted label: {label_file.name} -> {output_filename}")
                        
                    except Exception as e:
                        self.logger.error(f"Failed to convert label {label_file.name}: {e}")
                        self.stats['failed_labels'] += 1
                    
        self.logger.info(f"Label conversion completed: {self.stats['converted_labels']}/{self.stats['total_labels']} files")
        
    def run(self):
        """运行转换过程"""
        self.logger.info("="*80)
        self.logger.info("Starting TXT to NPY conversion...")
        self.logger.info("Mode: Single file conversion (no multi-view fusion)")
        self.logger.info("Output shape: (N, 65) - [frame_idx, 64 features]")
        self.logger.info("="*80)
        
        # 转换特征文件
        self.convert_features()
        
        # 转换标签文件
        self.convert_labels()
        
        # 输出统计信息
        self.logger.info("="*80)
        self.logger.info("Conversion Summary:")
        self.logger.info(f"  Features: {self.stats['converted_files']}/{self.stats['total_files']} converted")
        self.logger.info(f"  Labels: {self.stats['converted_labels']}/{self.stats['total_labels']} converted")
        self.logger.info(f"  Failed features: {self.stats['failed_files']}")
        self.logger.info(f"  Failed labels: {self.stats['failed_labels']}")
        self.logger.info("="*80)
        

def main():
    """主函数"""
    converter = TxtToNpyConverter()
    converter.run()
    

if __name__ == "__main__":
    main()
