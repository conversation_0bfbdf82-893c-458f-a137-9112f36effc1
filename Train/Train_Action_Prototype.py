#!/usr/bin/env python3
"""
动作原型训练脚本 - Train_Action_Prototype.py
根据Agent.md规范实现动作原型特征的训练

主要功能：
1. 加载训练数据
2. 按动作类别聚合特征
3. 计算每个动作的原型特征（均值）
4. 保存原型特征和统计信息
"""

import sys
import numpy as np
import yaml
import argparse
from pathlib import Path
from typing import Dict, List
from collections import defaultdict
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.data_loader import BreakfastDataLoader
from src.utils.file_utils import FileManager
from src.utils.visualization import VisualizationManager


class ActionPrototypeTrainer:
    """动作原型训练器"""

    def __init__(self, config: Dict, experiment_id: str = None, file_manager: FileManager = None):
        self.config = config
        self.experiment_id = experiment_id or datetime.now().strftime("%Y%m%d-%H%M%S")
        self.file_manager = file_manager

        # 初始化组件
        self.data_loader = BreakfastDataLoader(config)

        # 训练统计
        self.training_stats = {
            'total_samples': 0,
            'samples_per_action': {},
            'prototype_variances': {},
            'dimension_variances': []
        }

    def collect_action_features(self) -> Dict[int, List[np.ndarray]]:
        """收集每个动作类别的所有特征 - 使用多视角融合的数据加载器"""
        action_features = defaultdict(list)

        print("收集训练数据中的动作特征...")

        # 遍历所有训练数据
        for split in self.data_loader.train_splits:
            split_path = self.data_loader.npy_data_path / split
            if not split_path.exists():
                print(f"警告: 分割目录不存在: {split_path}")
                continue

            # 遍历任务目录
            for task_dir in split_path.iterdir():
                if not task_dir.is_dir():
                    continue

                task_name = task_dir.name
                
                try:
                    # 使用数据加载器的load_npy_data方法（会进行多视角融合）
                    # 使用非严格同步模式，以保留所有动作类别
                    features, labels = self.data_loader.load_npy_data(split, task_name, strict_sync=False)
                    
                    print(f"处理任务: {split}/{task_name} - {len(features)} 帧")
                    
                    # 按动作类别分组特征 - 收集所有帧的特征，不仅仅是转换帧
                    for i, label in enumerate(labels):
                        action_id = int(label)
                        action_features[action_id].append(features[i])

                except Exception as e:
                    print(f"  警告: 无法处理 {split}/{task_name}: {e}")

        # 转换为numpy数组
        for action_id in action_features:
            action_features[action_id] = np.array(action_features[action_id])
            self.training_stats['samples_per_action'][action_id] = len(action_features[action_id])

        self.training_stats['total_samples'] = sum(self.training_stats['samples_per_action'].values())

        print(f"收集完成: {len(action_features)} 个动作类别, 总计 {self.training_stats['total_samples']} 个样本")

        return action_features

    def compute_prototypes(self, action_features: Dict[int, np.ndarray]) -> np.ndarray:
        """计算动作原型特征"""
        print("计算动作原型特征...")

        # 获取动作类别数量和特征维度
        num_classes = self.data_loader.num_classes
        feature_dim = self.config['model']['feature_dim']

        # 初始化原型矩阵
        prototypes = np.zeros((num_classes, feature_dim), dtype=np.float32)

        # 计算每个动作的原型（均值）
        for action_id, features in action_features.items():
            if len(features) > 0:
                prototype = np.mean(features, axis=0)
                prototypes[action_id] = prototype

                # 计算方差用于统计
                variance = np.var(features, axis=0)
                self.training_stats['prototype_variances'][action_id] = np.mean(variance)

                print(f"动作 {action_id}: {len(features)} 个样本, 方差: {np.mean(variance):.6f}")

        # 计算特征维度的平均方差
        all_variances = []
        for features in action_features.values():
            if len(features) > 1:
                all_variances.append(np.var(features, axis=0))

        if all_variances:
            self.training_stats['dimension_variances'] = np.mean(all_variances, axis=0).tolist()

        print(f"原型计算完成: {prototypes.shape}")
        return prototypes

    def save_results(self, prototypes: np.ndarray):
        """保存训练结果"""
        if self.file_manager is None:
            print("警告: 文件管理器未设置，使用默认保存方式")
            # 创建默认输出目录
            output_dir = Path(self.config['output']['base_path']) / "Action_Prototype"
            output_dir.mkdir(parents=True, exist_ok=True)

            # 保存原型
            prototype_path = output_dir / f"action_prototypes_{self.experiment_id}.npy"
            np.save(prototype_path, prototypes)

            # 保存统计信息
            stats_path = output_dir / f"training_stats_{self.experiment_id}.json"
            import json
            with open(stats_path, 'w', encoding='utf-8') as f:
                json.dump(self.training_stats, f, indent=2, ensure_ascii=False)

            print(f"结果已保存到: {output_dir}")
        else:
            # 使用文件管理器保存
            # 保存原型参数
            prototype_dict = {'prototypes': prototypes}
            self.file_manager.save_model_parameters(
                prototype_dict,
                "Action_Prototype",
                "action_prototypes"
            )

            # 保存原始数据
            self.file_manager.save_raw_data(
                self.training_stats,
                "Action_Prototype",
                "training_stats",
                format='json'
            )

            # 创建可视化
            if 'visualization' in self.config:
                try:
                    viz_manager = VisualizationManager(self.config)

                    # 准备可视化数据 - 转换为plot_prototype_statistics期望的格式
                    viz_stats = {
                        'sample_counts': self.training_stats['samples_per_action'],
                        'variances': self.training_stats['prototype_variances'],
                        'dimension_variances': self.training_stats['dimension_variances']
                    }

                    # 绘制原型统计图
                    viz_path = self.file_manager.experiment_dir / "Action_Prototype" / "Visualization" / "prototype_stats.png"
                    viz_path.parent.mkdir(parents=True, exist_ok=True)
                    viz_manager.plot_prototype_statistics(viz_stats, str(viz_path))

                    print("可视化图表已生成")
                except Exception as e:
                    print(f"警告: 可视化生成失败: {e}")

    def train(self) -> Dict:
        """执行训练过程"""
        print("=" * 60)
        print("开始训练动作原型特征")
        print(f"实验ID: {self.experiment_id}")
        print("=" * 60)

        try:
            # 记录训练开始到Memory
            if self.file_manager:
                self.file_manager.log_to_memory(
                    "training",
                    "开始动作原型特征训练",
                    {"stage": "action_prototype", "status": "started"}
                )

            # 1. 收集动作特征
            action_features = self.collect_action_features()

            if not action_features:
                raise ValueError("未找到任何训练数据")

            # 2. 计算原型
            prototypes = self.compute_prototypes(action_features)

            # 3. 保存结果
            self.save_results(prototypes)

            # 4. 返回训练结果
            results = {
                'prototypes': prototypes,
                'training_stats': self.training_stats,
                'num_classes': self.data_loader.num_classes,
                'feature_dim': prototypes.shape[1]
            }

            # 记录训练完成到Memory
            if self.file_manager:
                self.file_manager.log_to_memory(
                    "training",
                    "动作原型特征训练完成",
                    {
                        "stage": "action_prototype",
                        "status": "completed",
                        "num_classes": len(action_features),
                        "total_samples": self.training_stats['total_samples']
                    }
                )

                # 保存实现笔记
                note_content = f"""
## 动作原型训练结果

### 训练统计
- 动作类别数: {len(action_features)}
- 总样本数: {self.training_stats['total_samples']}
- 特征维度: {prototypes.shape[1]}

### 每个动作的样本数
{chr(10).join([f"- 动作 {aid}: {count} 个样本" for aid, count in self.training_stats['samples_per_action'].items()])}

### 原型方差统计
{chr(10).join([f"- 动作 {aid}: {var:.6f}" for aid, var in self.training_stats['prototype_variances'].items()])}
"""
                self.file_manager.save_implementation_note(
                    "training_results",
                    "动作原型训练结果",
                    note_content
                )

            print("=" * 60)
            print("动作原型训练完成!")
            print(f"训练了 {len(action_features)} 个动作类别")
            print(f"总计 {self.training_stats['total_samples']} 个训练样本")
            print("=" * 60)

            return results

        except Exception as e:
            # 记录错误到Memory
            if self.file_manager:
                self.file_manager.log_to_memory(
                    "error",
                    f"动作原型训练失败: {str(e)}",
                    {"stage": "action_prototype", "status": "failed", "error": str(e)}
                )

            print(f"训练失败: {e}")
            import traceback
            traceback.print_exc()
            return {'error': str(e)}


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='动作原型训练')
    parser.add_argument('--config', type=str, default='configs/config.yaml',
                       help='配置文件路径')
    parser.add_argument('--exp_name', type=str, default=None,
                       help='实验名称/ID')
    args = parser.parse_args()

    # 加载配置
    config_path = Path(args.config)
    if not config_path.is_absolute():
        config_path = Path(__file__).parent.parent / config_path
        
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)

    # 创建实验ID
    if args.exp_name:
        experiment_id = args.exp_name.replace(' ', '_').replace(':', '')
    else:
        experiment_id = datetime.now().strftime("%Y%m%d-%H%M%S")

    # 创建文件管理器
    file_manager = FileManager(
        base_output_path=config['output']['base_path'],
        experiment_name=f"action_prototype_{experiment_id}",
        memory_config={'base_path': './Memory', 'enable_logging': True}
    )

    # 创建训练器
    trainer = ActionPrototypeTrainer(config, experiment_id=experiment_id, file_manager=file_manager)

    # 执行训练
    results = trainer.train()

    if 'error' not in results:
        print("✅ 动作原型训练成功完成!")
    else:
        print("❌ 动作原型训练失败!")

if __name__ == "__main__":
    main()
