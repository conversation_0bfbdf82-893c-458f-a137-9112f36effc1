#!/usr/bin/env python3
"""
静态任务图训练脚本 - Train_Static_Model.py
根据IDEA.md第2.3节规范实现

主要功能：
1. 加载预计算的静态任务图边权重W^0
2. 训练静态基线模型，学习可学习的偏置向量
3. 使用训练集(s1, s2, s3)进行训练
4. 保存static_model_bias.pt
5. 生成训练过程的详细数据和可视化图表
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import json
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
from torch.utils.data import Dataset, DataLoader

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置可视化风格
sns.set_theme(style="whitegrid")

# 全局配置
DATA_ROOT = "/data2/syd_data/Breakfast_Data"
LABEL_MAP_PATH = os.path.join(DATA_ROOT, "label_map.json")
TRAIN_DATA_DIR = os.path.join(DATA_ROOT, "breakfast_data_npy")
TRAIN_LABEL_DIR = os.path.join(DATA_ROOT, "segmentation_coarse_npy")

# 输入路径配置（从前面步骤的输出）
EDGE_WEIGHT_DIR = os.path.join(DATA_ROOT, "Outputs", "Edge_Weight", "Model_parameters")

# 输出路径配置（符合IDEA.md第5章规范）
OUTPUT_BASE = os.path.join(DATA_ROOT, "Outputs", "Static")
MODEL_PARAMS_DIR = os.path.join(OUTPUT_BASE, "Model_parameters")
RAW_DATA_DIR = os.path.join(OUTPUT_BASE, "Raw_data")
VISUALIZATION_DIR = os.path.join(OUTPUT_BASE, "Visualization")

# 生成时间戳ID
TIMESTAMP_ID = datetime.now().strftime("%Y%m%d-%H%M%S")

# 训练超参数
MAX_EPOCHS = 10
BATCH_SIZE = 32
LEARNING_RATE = 1e-3
RANDOM_SEED = 42


class TransitionDataset(Dataset):
    """动作转移数据集"""
    
    def __init__(self, data_dir: str, label_dir: str, label_map: Dict[str, str], split: str = "train"):
        self.data_dir = data_dir
        self.label_dir = label_dir
        self.label_map = label_map
        self.split = split
        self.M = len(label_map)
        
        self.samples = self.load_samples()
        print(f"📊 {split}数据集加载完成: {len(self.samples)} 个转移样本")
    
    def load_samples(self) -> List[Tuple[torch.Tensor, int, int]]:
        """加载转移样本"""
        samples = []
        
        # 根据split选择受试者
        subjects = ["s1", "s2", "s3"] if self.split == "train" else ["s4"]
        
        for subject in subjects:
            subject_feat_dir = os.path.join(self.data_dir, subject)
            subject_label_dir = os.path.join(self.label_dir, f"{subject}_label")
            
            if not os.path.exists(subject_feat_dir) or not os.path.exists(subject_label_dir):
                continue
            
            # 遍历任务目录
            for task_dir in os.listdir(subject_feat_dir):
                feat_task_path = os.path.join(subject_feat_dir, task_dir)
                label_task_path = os.path.join(subject_label_dir, task_dir)
                
                if not os.path.isdir(feat_task_path) or not os.path.isdir(label_task_path):
                    continue
                
                # 处理每个文件
                for feat_file in os.listdir(feat_task_path):
                    if feat_file.endswith('.npy'):
                        feat_path = os.path.join(feat_task_path, feat_file)
                        label_path = os.path.join(label_task_path, feat_file)
                        
                        if os.path.exists(label_path):
                            file_samples = self.extract_transitions(feat_path, label_path)
                            samples.extend(file_samples)
        
        return samples
    
    def extract_transitions(self, feat_path: str, label_path: str) -> List[Tuple[torch.Tensor, int, int]]:
        """从单个文件中提取转移样本"""
        try:
            features = torch.from_numpy(np.load(feat_path)).float()  # (N, D)
            labels = torch.from_numpy(np.load(label_path)).long()    # (N,)
            
            # 确保维度匹配
            min_len = min(len(features), len(labels))
            features = features[:min_len]
            labels = labels[:min_len]
            
            transitions = []
            
            # 提取动作段边界的转移
            for k in range(len(labels) - 1):
                n_k = labels[k].item()
                n_k_plus_1 = labels[k + 1].item()
                
                # 只在动作切换时创建样本
                if n_k != n_k_plus_1:
                    V_k = features[k]  # 当前段最后一帧的特征
                    transitions.append((V_k, n_k, n_k_plus_1))
            
            return transitions
            
        except Exception as e:
            print(f"⚠️ 处理文件失败 {feat_path}: {e}")
            return []
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        return self.samples[idx]


class StaticModel(nn.Module):
    """静态任务图模型"""
    
    def __init__(self, edge_weights: torch.Tensor):
        """
        Args:
            edge_weights: 静态边权重矩阵 W^0 (M, M)
        """
        super().__init__()
        
        self.M = edge_weights.size(0)
        
        # 注册静态边权重为buffer（不参与梯度更新）
        self.register_buffer('edge_weights', edge_weights)
        
        # 可学习的偏置向量
        self.bias = nn.Parameter(torch.zeros(self.M))
    
    def forward(self, current_actions: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            current_actions: 当前动作索引 (batch_size,)
            
        Returns:
            logits: 下一动作的logits (batch_size, M)
        """
        # 根据当前动作索引选取对应行
        logits = self.edge_weights[current_actions] + self.bias
        return logits


class StaticModelTrainer:
    """静态模型训练器"""
    
    def __init__(self):
        self.label_map = self.load_label_map()
        self.M = len(self.label_map)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建输出目录
        os.makedirs(MODEL_PARAMS_DIR, exist_ok=True)
        os.makedirs(RAW_DATA_DIR, exist_ok=True)
        os.makedirs(VISUALIZATION_DIR, exist_ok=True)
        
        # 设置随机种子
        torch.manual_seed(RANDOM_SEED)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(RANDOM_SEED)
        
        print(f"🏗️ 静态模型训练器初始化")
        print(f"   - 动作类别数: {self.M}")
        print(f"   - 设备: {self.device}")
        print(f"   - 时间戳ID: {TIMESTAMP_ID}")
    
    def load_label_map(self) -> Dict[str, str]:
        """加载标签映射"""
        with open(LABEL_MAP_PATH, 'r', encoding='utf-8') as f:
            label_map = json.load(f)
        return label_map
    
    def load_edge_weights(self) -> torch.Tensor:
        """加载预计算的边权重"""
        print(f"🔍 查找边权重文件在: {EDGE_WEIGHT_DIR}")

        if not os.path.exists(EDGE_WEIGHT_DIR):
            raise FileNotFoundError(f"边权重目录不存在: {EDGE_WEIGHT_DIR}")

        # 查找最新的边权重文件
        all_files = os.listdir(EDGE_WEIGHT_DIR)
        print(f"   - 目录中的所有文件: {all_files}")

        edge_weight_files = [f for f in all_files if f.startswith('edge_weights_') and f.endswith('.pt')]
        print(f"   - 找到的边权重文件: {edge_weight_files}")

        if not edge_weight_files:
            raise FileNotFoundError(f"未找到边权重文件在 {EDGE_WEIGHT_DIR}")

        # 使用最新的文件（按文件名排序）
        latest_file = sorted(edge_weight_files)[-1]
        edge_weight_path = os.path.join(EDGE_WEIGHT_DIR, latest_file)

        print(f"📥 加载边权重: {edge_weight_path}")

        W0 = torch.load(edge_weight_path, map_location='cpu')
        print(f"   - 形状: {W0.shape}")
        print(f"   - 数据类型: {W0.dtype}")

        return W0

    def create_data_loaders(self) -> Tuple[DataLoader, DataLoader]:
        """创建数据加载器"""
        print("📊 创建数据加载器...")

        # 创建训练和验证数据集
        train_dataset = TransitionDataset(TRAIN_DATA_DIR, TRAIN_LABEL_DIR, self.label_map, "train")
        val_dataset = TransitionDataset(TRAIN_DATA_DIR, TRAIN_LABEL_DIR, self.label_map, "test")

        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False)

        print(f"   - 训练样本: {len(train_dataset)}")
        print(f"   - 验证样本: {len(val_dataset)}")

        return train_loader, val_loader

    def train_epoch(self, model: StaticModel, train_loader: DataLoader,
                   optimizer: torch.optim.Optimizer, epoch: int) -> float:
        """训练一个epoch"""
        model.train()
        total_loss = 0.0
        num_batches = 0

        for batch_idx, (features, current_actions, next_actions) in enumerate(train_loader):
            # 移动到设备
            current_actions = current_actions.to(self.device)
            next_actions = next_actions.to(self.device)

            # 前向传播
            logits = model(current_actions)

            # 计算损失
            loss = F.cross_entropy(logits, next_actions)

            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            total_loss += loss.item()
            num_batches += 1

            if batch_idx % 100 == 0:
                print(f"   Epoch {epoch}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}")

        avg_loss = total_loss / num_batches if num_batches > 0 else 0.0
        return avg_loss

    def validate_epoch(self, model: StaticModel, val_loader: DataLoader) -> Tuple[float, float]:
        """验证一个epoch"""
        model.eval()
        total_loss = 0.0
        correct = 0
        total = 0

        with torch.no_grad():
            for features, current_actions, next_actions in val_loader:
                current_actions = current_actions.to(self.device)
                next_actions = next_actions.to(self.device)

                logits = model(current_actions)
                loss = F.cross_entropy(logits, next_actions)

                total_loss += loss.item()

                # 计算准确率
                pred = torch.argmax(logits, dim=1)
                correct += (pred == next_actions).sum().item()
                total += next_actions.size(0)

        avg_loss = total_loss / len(val_loader)
        accuracy = correct / total if total > 0 else 0.0

        return avg_loss, accuracy

    def train_model(self, model: StaticModel, train_loader: DataLoader,
                   val_loader: DataLoader) -> Dict:
        """训练模型"""
        print("🚀 开始训练静态模型...")

        optimizer = torch.optim.Adam(model.parameters(), lr=LEARNING_RATE)

        # 训练历史
        history = {
            'train_loss': [],
            'val_loss': [],
            'val_accuracy': []
        }

        best_val_loss = float('inf')
        best_model_state = None

        for epoch in range(MAX_EPOCHS):
            print(f"\n📈 Epoch {epoch + 1}/{MAX_EPOCHS}")

            # 训练
            train_loss = self.train_epoch(model, train_loader, optimizer, epoch + 1)

            # 验证
            val_loss, val_accuracy = self.validate_epoch(model, val_loader)

            # 记录历史
            history['train_loss'].append(train_loss)
            history['val_loss'].append(val_loss)
            history['val_accuracy'].append(val_accuracy)

            print(f"   训练损失: {train_loss:.4f}")
            print(f"   验证损失: {val_loss:.4f}")
            print(f"   验证准确率: {val_accuracy:.4f}")

            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                best_model_state = model.state_dict().copy()
                print(f"   ✅ 新的最佳模型 (验证损失: {val_loss:.4f})")

        # 加载最佳模型
        if best_model_state is not None:
            model.load_state_dict(best_model_state)

        print(f"\n✅ 训练完成! 最佳验证损失: {best_val_loss:.4f}")

        return history

    def save_results(self, model: StaticModel, history: Dict):
        """保存训练结果"""
        print("💾 保存训练结果...")

        # 1. 保存模型参数
        model_path = os.path.join(MODEL_PARAMS_DIR, f"static_model_bias_{TIMESTAMP_ID}.pt")
        torch.save(model.bias, model_path)
        print(f"   ✅ 静态模型偏置已保存: {model_path}")

        # 2. 保存完整模型状态
        full_model_path = os.path.join(MODEL_PARAMS_DIR, f"static_model_full_{TIMESTAMP_ID}.pt")
        torch.save({
            'model_state_dict': model.state_dict(),
            'edge_weights': model.edge_weights,
            'bias': model.bias,
            'num_classes': self.M
        }, full_model_path)
        print(f"   ✅ 完整模型已保存: {full_model_path}")

        # 3. 保存原始数据
        raw_data = {
            'training_history': history,
            'final_bias': model.bias,
            'edge_weights': model.edge_weights,
            'metadata': {
                'timestamp_id': TIMESTAMP_ID,
                'num_classes': self.M,
                'max_epochs': MAX_EPOCHS,
                'batch_size': BATCH_SIZE,
                'learning_rate': LEARNING_RATE,
                'random_seed': RANDOM_SEED,
                'label_map': self.label_map
            }
        }

        raw_data_path = os.path.join(RAW_DATA_DIR, f"static_model_raw_data_{TIMESTAMP_ID}.pt")
        torch.save(raw_data, raw_data_path)

        # 保存训练历史为JSON
        history_path = os.path.join(RAW_DATA_DIR, f"static_training_history_{TIMESTAMP_ID}.json")
        with open(history_path, 'w', encoding='utf-8') as f:
            json.dump(history, f, indent=2)

        print(f"   ✅ 原始数据已保存: {raw_data_path}")
        print(f"   ✅ 训练历史已保存: {history_path}")

    def generate_visualizations(self, history: Dict):
        """生成可视化图表"""
        print("📊 生成可视化图表...")

        # 1. 训练/验证损失曲线
        plt.figure(figsize=(12, 5))

        plt.subplot(1, 2, 1)
        epochs = range(1, len(history['train_loss']) + 1)
        plt.plot(epochs, history['train_loss'], 'b-', label='Training Loss')
        plt.plot(epochs, history['val_loss'], 'r-', label='Validation Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Training/Validation Loss Curves')
        plt.legend()
        plt.grid(True)

        # 2. 验证准确率曲线
        plt.subplot(1, 2, 2)
        plt.plot(epochs, history['val_accuracy'], 'g-', label='Validation Accuracy')
        plt.xlabel('Epoch')
        plt.ylabel('Accuracy')
        plt.title('Validation Accuracy Curve')
        plt.legend()
        plt.grid(True)

        plt.tight_layout()
        loss_curve_path = os.path.join(VISUALIZATION_DIR, f"training_curves_{TIMESTAMP_ID}.png")
        plt.savefig(loss_curve_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"   ✅ 可视化图表已保存到: {VISUALIZATION_DIR}")

    def run_training(self):
        """执行完整的训练流程"""
        print("=" * 80)
        print("🚀 静态任务图训练开始")
        print("=" * 80)

        # 1. 加载边权重
        W0 = self.load_edge_weights()

        # 2. 创建数据加载器
        train_loader, val_loader = self.create_data_loaders()

        # 3. 创建模型
        model = StaticModel(W0).to(self.device)

        print(f"🧠 模型参数统计:")
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"   - 总参数: {total_params:,}")
        print(f"   - 可训练参数: {trainable_params:,}")

        # 4. 训练模型
        history = self.train_model(model, train_loader, val_loader)

        # 5. 保存结果
        self.save_results(model, history)

        # 6. 生成可视化
        self.generate_visualizations(history)

        print("=" * 80)
        print("✅ 静态任务图训练完成!")
        print(f"📁 输出目录: {OUTPUT_BASE}")
        print(f"🏷️ 时间戳ID: {TIMESTAMP_ID}")
        print("=" * 80)


def main():
    """主函数"""
    trainer = StaticModelTrainer()
    trainer.run_training()


if __name__ == "__main__":
    main()
