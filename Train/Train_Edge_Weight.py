#!/usr/bin/env python3
"""
任务图边权训练脚本 - Train_Edge_Weight.py
根据IDEA.md第2.2节规范实现

主要功能：
1. 统计训练集中所有相邻动作的转移频次
2. 计算平滑后的条件概率P(n_j|n_i)
3. 取对数得到初始的边权重W_ij^0
4. 保存edge_weights.pt和edge_weights.csv
5. 生成训练过程的详细数据和可视化图表
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
import json
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.models.base_trainer import BaseTrainer

# 设置可视化风格
sns.set_theme(style="whitegrid")


class EdgeWeightTrainer(BaseTrainer):
    """任务图边权训练器"""
    
    def __init__(self, config: Dict, experiment_id: str = None, file_manager = None, alpha: float = 1.0):
        """
        Args:
            config: 配置字典
            experiment_id: 实验ID（时间戳）
            file_manager: 文件管理器
            alpha: 平滑因子（Laplace平滑中，alpha=1）
        """
        super().__init__(config, experiment_id, file_manager)
        
        self.alpha = alpha
        self.label_map = self.load_label_map()
        self.M = len(self.label_map)  # 动作类别总数
        
        self.file_manager.logger.info(f"任务图边权训练器初始化")
        self.file_manager.logger.info(f"动作类别数: {self.M}")
        self.file_manager.logger.info(f"平滑因子α: {self.alpha}")
    
    def get_trainer_name(self) -> str:
        """获取训练器名称"""
        return "Edge_Weight"
    
    def find_training_label_files(self) -> List[str]:
        """查找训练标签文件"""
        label_files = []
        
        # 从配置获取路径
        train_label_dir = Path(self.config['data']['npy_label_path'])
        
        # 扫描训练集的标签
        for subject in self.config['training']['train_splits']:
            subject_label_dir = train_label_dir / f"{subject}_label"
            
            if not subject_label_dir.exists():
                self.file_manager.logger.warning(f"跳过不存在的目录: {subject_label_dir}")
                continue
            
            # 递归查找所有.npy文件
            for label_file in subject_label_dir.rglob("*.npy"):
                label_files.append(str(label_file))
        
        self.file_manager.logger.info(f"找到 {len(label_files)} 个训练标签文件")
        return label_files
    
    def compute_transition_counts(self, label_files: List[str]) -> Tuple[Dict, Dict, Dict]:
        """
        计算转移频次
        
        Returns:
            transitions: 转移计数字典 {(i, j): count}
            totals: 每个动作的总出现次数 {i: count}
            stats: 统计信息
        """
        self.file_manager.logger.info("开始计算转移频次...")
        
        transitions = defaultdict(int)
        totals = defaultdict(int)
        
        stats = {
            'total_transitions': 0,
            'unique_transitions': 0,
            'processed_files': 0,
            'skipped_files': 0,
            'total_frames': 0,
            'transitions_per_action': defaultdict(int)
        }
        
        # 遍历所有标签文件
        for i, label_path in enumerate(label_files):
            try:
                # 加载标签序列
                labels = np.load(label_path)  # (N,)
                
                # 找出动作转换点
                prev_label = labels[0]
                for j in range(1, len(labels)):
                    curr_label = labels[j]
                    
                    # 只在动作变化时记录转移
                    if prev_label != curr_label:
                        u, v = int(prev_label), int(curr_label)
                        transitions[(u, v)] += 1
                        totals[u] += 1
                        stats['total_transitions'] += 1
                        stats['transitions_per_action'][u] += 1
                        
                    prev_label = curr_label
                
                stats['processed_files'] += 1
                stats['total_frames'] += len(labels)
                
                if (i + 1) % 50 == 0:
                    self.file_manager.logger.info(f"处理进度: {i + 1}/{len(label_files)}")
                
            except Exception as e:
                self.file_manager.logger.error(f"处理文件失败 {label_path}: {e}")
                stats['skipped_files'] += 1
                continue
        
        stats['unique_transitions'] = len(transitions)
        stats['transitions_per_action'] = dict(stats['transitions_per_action'])
        
        self.file_manager.logger.info(f"转移频次计算完成:")
        self.file_manager.logger.info(f"  - 总转移数: {stats['total_transitions']}")
        self.file_manager.logger.info(f"  - 唯一转移数: {stats['unique_transitions']}")
        self.file_manager.logger.info(f"  - 处理文件数: {stats['processed_files']}")
        
        return dict(transitions), dict(totals), stats
    
    def compute_edge_weights(self, transitions: Dict, totals: Dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        计算边权重矩阵
        
        根据IDEA.md公式：
        W_ij^0 = log(P(n_j|n_i)) = log((C_ij + α) / (Σ_k(C_ik + α)))
        
        Returns:
            W0: 边权重矩阵 (M, M)
            prob_matrix: 条件概率矩阵
            count_matrix: 原始计数矩阵
        """
        self.file_manager.logger.info("计算边权重矩阵...")
        
        # 初始化矩阵
        W0 = torch.zeros((self.M, self.M), dtype=torch.float32)
        prob_matrix = torch.zeros((self.M, self.M), dtype=torch.float32)
        count_matrix = torch.zeros((self.M, self.M), dtype=torch.float32)
        
        # 填充计数矩阵
        for (i, j), count in transitions.items():
            if 0 <= i < self.M and 0 <= j < self.M:
                count_matrix[i, j] = count
        
        # 计算条件概率和边权重
        for i in range(self.M):
            total_i = totals.get(i, 0)
            
            for j in range(self.M):
                count_ij = transitions.get((i, j), 0)
                
                # 平滑后的条件概率
                prob_ij = (count_ij + self.alpha) / (total_i + self.M * self.alpha)
                prob_matrix[i, j] = prob_ij
                
                # 边权重（对数概率）
                W0[i, j] = torch.log(torch.tensor(prob_ij))
        
        self.file_manager.logger.info(f"边权重矩阵计算完成")
        
        return W0, prob_matrix, count_matrix

    def save_results(self, W0: torch.Tensor, prob_matrix: torch.Tensor,
                    count_matrix: torch.Tensor, transitions: Dict, totals: Dict, stats: Dict):
        """保存训练结果"""
        self.file_manager.logger.info("保存训练结果...")

        # 1. 保存模型参数
        self.save_model_parameters(W0, "edge_weights")

        # 2. 保存CSV格式（便于人工查看）
        csv_path = self.model_params_dir / f"edge_weights_{self.experiment_id}.csv"
        action_names = [self.label_map.get(str(i), f"Action_{i}") for i in range(self.M)]

        df = pd.DataFrame(W0.numpy(), index=action_names, columns=action_names)
        df.to_csv(csv_path)
        self.file_manager.logger.info(f"边权重CSV已保存: {csv_path}")

        # 3. 保存原始数据
        raw_data = {
            'edge_weights': W0,
            'probability_matrix': prob_matrix,
            'count_matrix': count_matrix,
            'transitions': transitions,
            'totals': totals,
            'statistics': stats,
            'metadata': {
                'timestamp_id': self.experiment_id,
                'num_classes': self.M,
                'smoothing_alpha': self.alpha,
                'label_map': self.label_map
            }
        }

        self.save_raw_data(raw_data, "edge_weight_raw_data")

    def generate_visualizations(self, W0: torch.Tensor, prob_matrix: torch.Tensor,
                              count_matrix: torch.Tensor, stats: Dict):
        """生成可视化图表"""
        self.file_manager.logger.info("生成可视化图表...")

        action_names = [self.label_map.get(str(i), f"Action_{i}") for i in range(self.M)]

        # 1. 原始转移频次矩阵热力图
        plt.figure(figsize=(14, 12))

        # 只显示有数据的类别
        nonzero_mask = (count_matrix.sum(dim=1) > 0) | (count_matrix.sum(dim=0) > 0)
        if nonzero_mask.any():
            valid_indices = torch.where(nonzero_mask)[0]
            valid_count_matrix = count_matrix[valid_indices][:, valid_indices]
            valid_names = [action_names[i] for i in valid_indices]

            sns.heatmap(valid_count_matrix.numpy(),
                       xticklabels=valid_names,
                       yticklabels=valid_names,
                       annot=True, fmt='.0f',
                       cmap='Blues')
            plt.title('原始转移频次矩阵')
            plt.xlabel('目标动作')
            plt.ylabel('源动作')
            plt.tight_layout()

            count_heatmap_path = self.visualization_dir / f"transition_counts_{self.experiment_id}.png"
            plt.savefig(count_heatmap_path, dpi=300, bbox_inches='tight')
            plt.close()

        # 2. 条件概率矩阵热力图
        plt.figure(figsize=(14, 12))

        if nonzero_mask.any():
            valid_prob_matrix = prob_matrix[valid_indices][:, valid_indices]

            sns.heatmap(valid_prob_matrix.numpy(),
                       xticklabels=valid_names,
                       yticklabels=valid_names,
                       annot=True, fmt='.3f',
                       cmap='viridis')
            plt.title('条件概率矩阵 P(n_j|n_i)')
            plt.xlabel('目标动作')
            plt.ylabel('源动作')
            plt.tight_layout()

            prob_heatmap_path = self.visualization_dir / f"transition_probabilities_{self.experiment_id}.png"
            plt.savefig(prob_heatmap_path, dpi=300, bbox_inches='tight')
            plt.close()

        # 3. 边权重矩阵热力图
        plt.figure(figsize=(14, 12))

        if nonzero_mask.any():
            valid_W0 = W0[valid_indices][:, valid_indices]

            sns.heatmap(valid_W0.numpy(),
                       xticklabels=valid_names,
                       yticklabels=valid_names,
                       annot=True, fmt='.2f',
                       cmap='RdBu_r', center=0)
            plt.title('边权重矩阵 W₀ (log概率)')
            plt.xlabel('目标动作')
            plt.ylabel('源动作')
            plt.tight_layout()

            weight_heatmap_path = self.visualization_dir / f"edge_weights_{self.experiment_id}.png"
            plt.savefig(weight_heatmap_path, dpi=300, bbox_inches='tight')
            plt.close()

        self.file_manager.logger.info(f"可视化图表已保存到: {self.visualization_dir}")

    def train(self) -> Dict:
        """执行训练过程"""
        try:
            # 记录训练开始
            self.log_to_memory("training", "开始任务图边权训练", 
                             {"stage": "edge_weight", "status": "started"})

            # 1. 查找训练标签文件
            label_files = self.find_training_label_files()
            if not label_files:
                raise ValueError("未找到训练标签文件!")

            # 2. 计算转移频次
            transitions, totals, stats = self.compute_transition_counts(label_files)

            # 3. 计算边权重
            W0, prob_matrix, count_matrix = self.compute_edge_weights(transitions, totals)

            # 4. 保存结果
            self.save_results(W0, prob_matrix, count_matrix, transitions, totals, stats)

            # 5. 生成可视化
            self.generate_visualizations(W0, prob_matrix, count_matrix, stats)

            # 记录训练完成
            self.log_to_memory("training", "任务图边权训练完成",
                             {"stage": "edge_weight", "status": "completed",
                              "unique_transitions": stats['unique_transitions'],
                              "total_transitions": stats['total_transitions']})

            self.file_manager.logger.info("任务图边权训练完成!")
            
            return {
                'edge_weights': W0,
                'statistics': stats,
                'metadata': {
                    'num_classes': self.M,
                    'smoothing_alpha': self.alpha,
                    'timestamp_id': self.experiment_id
                }
            }

        except Exception as e:
            self.log_to_memory("error", f"任务图边权训练失败: {str(e)}",
                             {"stage": "edge_weight", "status": "failed"})
            raise e


def main():
    """主函数"""
    import yaml
    
    # 加载配置
    config_path = Path(__file__).parent.parent / "configs" / "config.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 创建训练器
    trainer = EdgeWeightTrainer(config, alpha=1.0)
    
    # 执行训练
    results = trainer.train()
    
    if results:
        print("✅ 任务图边权训练成功完成!")
    else:
        print("❌ 任务图边权训练失败!")


if __name__ == "__main__":
    main()
