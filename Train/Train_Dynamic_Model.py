#!/usr/bin/env python3
"""
动态任务图训练脚本 - Train_Dynamic_Model.py
根据IDEA.md第2.4节规范实现

主要功能：
1. 加载预计算的原型特征和静态图边权重
2. 训练MLP_diff网络进行动态权重调整
3. 使用训练集(s1, s2, s3)进行训练
4. 保存dynamic_model_mlp.pt
5. 生成训练过程的详细数据和可视化图表
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import json
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
from torch.utils.data import Dataset, DataLoader

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置可视化风格
sns.set_theme(style="whitegrid")

# 全局配置
DATA_ROOT = "/data2/syd_data/Breakfast_Data"
LABEL_MAP_PATH = os.path.join(DATA_ROOT, "label_map.json")
TRAIN_DATA_DIR = os.path.join(DATA_ROOT, "breakfast_data_npy")
TRAIN_LABEL_DIR = os.path.join(DATA_ROOT, "segmentation_coarse_npy")

# 输入路径配置（从前面步骤的输出）
PROTOTYPE_DIR = os.path.join(DATA_ROOT, "Outputs", "Action_Prototype", "Model_parameters")
EDGE_WEIGHT_DIR = os.path.join(DATA_ROOT, "Outputs", "Edge_Weight", "Model_parameters")

# 输出路径配置（符合IDEA.md第5章规范）
OUTPUT_BASE = os.path.join(DATA_ROOT, "Outputs", "Dynamic")
MODEL_PARAMS_DIR = os.path.join(OUTPUT_BASE, "Model_parameters")
RAW_DATA_DIR = os.path.join(OUTPUT_BASE, "Raw_data")
VISUALIZATION_DIR = os.path.join(OUTPUT_BASE, "Visualization")

# 生成时间戳ID
TIMESTAMP_ID = datetime.now().strftime("%Y%m%d-%H%M%S")

# 训练超参数
MAX_EPOCHS = 10
BATCH_SIZE = 32
LEARNING_RATE = 1e-4
RANDOM_SEED = 42

# 模型超参数
FEATURE_DIM = 64  # D
HIDDEN_DIM = 256  # H
ALPHA = 0.05      # 动态更新权重因子


class MLPDiff(nn.Module):
    """差分更新MLP网络"""
    
    def __init__(self, D: int, M: int, H: int = 256, alpha: float = 0.05):
        """
        Args:
            D: 输入特征维度
            M: 动作类别数量（输出维度）
            H: 隐藏层维度
            alpha: 输出缩放因子
        """
        super().__init__()
        self.alpha = alpha
        
        # 两层全连接网络
        self.mlp = nn.Sequential(
            nn.Linear(D, H),
            nn.ReLU(),
            nn.Linear(H, M),
            nn.Tanh()  # 输出范围 (-1, 1)
        )
    
    def forward(self, diff: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            diff: 差分特征，形状为 (batch_size, D)
            
        Returns:
            调整量，形状为 (batch_size, M)，范围在 (-alpha, alpha)
        """
        return self.alpha * self.mlp(diff)


class DynamicTransitionDataset(Dataset):
    """动态模型的动作转移数据集"""
    
    def __init__(self, data_dir: str, label_dir: str, label_map: Dict[str, str], 
                 prototypes: torch.Tensor, split: str = "train"):
        self.data_dir = data_dir
        self.label_dir = label_dir
        self.label_map = label_map
        self.prototypes = prototypes
        self.split = split
        self.M = len(label_map)
        
        self.samples = self.load_samples()
        print(f"📊 {split}数据集加载完成: {len(self.samples)} 个转移样本")
    
    def load_samples(self) -> List[Tuple[torch.Tensor, int, int]]:
        """加载转移样本"""
        samples = []

        # 根据split选择受试者
        subjects = ["s1", "s2", "s3"] if self.split == "train" else ["s4"]

        for subject in subjects:
            subject_feat_dir = os.path.join(self.data_dir, subject)
            subject_label_dir = os.path.join(self.label_dir, f"{subject}_label")

            if not os.path.exists(subject_feat_dir) or not os.path.exists(subject_label_dir):
                continue

            # 遍历任务目录
            for task_dir in os.listdir(subject_feat_dir):
                feat_task_path = os.path.join(subject_feat_dir, task_dir)
                label_task_path = os.path.join(subject_label_dir, task_dir)

                if not os.path.isdir(feat_task_path) or not os.path.isdir(label_task_path):
                    continue

                # 处理每个文件
                for feat_file in os.listdir(feat_task_path):
                    if feat_file.endswith('.npy'):
                        feat_path = os.path.join(feat_task_path, feat_file)
                        label_path = os.path.join(label_task_path, feat_file)

                        if os.path.exists(label_path):
                            file_samples = self.extract_transitions(feat_path, label_path)
                            samples.extend(file_samples)

        return samples
    
    def extract_transitions(self, feat_path: str, label_path: str) -> List[Tuple[torch.Tensor, int, int]]:
        """从单个文件中提取转移样本"""
        try:
            features = torch.from_numpy(np.load(feat_path)).float()  # (N, D)
            labels = torch.from_numpy(np.load(label_path)).long()    # (N,)

            # 确保维度匹配
            min_len = min(len(features), len(labels))
            features = features[:min_len]
            labels = labels[:min_len]

            transitions = []

            # 提取动作段边界的转移
            for k in range(len(labels) - 1):
                n_k = labels[k].item()
                n_k_plus_1 = labels[k + 1].item()

                # 只在动作切换时创建样本
                if n_k != n_k_plus_1:
                    V_k = features[k]  # 当前段最后一帧的特征

                    # 只保存必要的数据：features, current_action, next_action
                    # 差分特征将在训练时实时计算
                    if 0 <= n_k < self.M and 0 <= n_k_plus_1 < self.M:
                        transitions.append((V_k, n_k, n_k_plus_1))

            return transitions

        except Exception as e:
            print(f"⚠️ 处理文件失败 {feat_path}: {e}")
            return []
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        return self.samples[idx]


class DynamicModel(nn.Module):
    """动态任务图模型"""
    
    def __init__(self, edge_weights: torch.Tensor, prototypes: torch.Tensor, 
                 D: int, M: int, H: int = 256, alpha: float = 0.05):
        """
        Args:
            edge_weights: 静态边权重矩阵 W^0 (M, M)
            prototypes: 动作原型矩阵 (M, D)
            D: 特征维度
            M: 动作类别数
            H: 隐藏层维度
            alpha: 动态更新因子
        """
        super().__init__()
        
        self.M = M
        
        # 注册静态权重和原型为buffer（不参与梯度更新）
        self.register_buffer('edge_weights', edge_weights)
        self.register_buffer('prototypes', prototypes)
        
        # 差分更新网络（唯一的可训练部分）
        self.mlp_diff = MLPDiff(D, M, H, alpha)
    
    def forward(self, features: torch.Tensor, current_actions: torch.Tensor) -> torch.Tensor:
        """
        前向传播（向量化优化版本）

        Args:
            features: 输入特征 (batch_size, D)
            current_actions: 当前动作索引 (batch_size,)

        Returns:
            logits: 下一动作的logits (batch_size, M)
        """
        batch_size = features.size(0)

        # 向量化计算差分特征
        # 使用 torch.gather 或 index_select 一次性获取对应原型
        # 确保 current_actions 在有效范围内
        valid_mask = (current_actions >= 0) & (current_actions < self.M)

        # 创建安全的索引（将无效索引设为0，后续会被mask掉）
        safe_actions = torch.where(valid_mask, current_actions, torch.zeros_like(current_actions))

        # 批量获取对应的原型特征 (batch_size, D)
        selected_prototypes = self.prototypes[safe_actions]

        # 批量计算差分特征
        diff_features = torch.abs(features - selected_prototypes)

        # 对无效样本的差分特征置零
        diff_features = torch.where(valid_mask.unsqueeze(1), diff_features, torch.zeros_like(diff_features))

        # 通过MLP预测权重调整量
        delta_W = self.mlp_diff(diff_features)  # (batch_size, M)

        # 向量化计算logits：W^0[n_k] + ΔW_k
        # 批量获取对应的边权重 (batch_size, M)
        selected_edge_weights = self.edge_weights[safe_actions]

        # 计算logits
        logits = selected_edge_weights + delta_W

        # 对无效样本的logits置零
        logits = torch.where(valid_mask.unsqueeze(1), logits, torch.zeros_like(logits))

        return logits


class DynamicModelTrainer:
    """动态模型训练器"""

    def __init__(self):
        self.label_map = self.load_label_map()
        self.M = len(self.label_map)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 创建输出目录
        os.makedirs(MODEL_PARAMS_DIR, exist_ok=True)
        os.makedirs(RAW_DATA_DIR, exist_ok=True)
        os.makedirs(VISUALIZATION_DIR, exist_ok=True)

        # 设置随机种子
        torch.manual_seed(RANDOM_SEED)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(RANDOM_SEED)

        print(f"🧠 动态模型训练器初始化")
        print(f"   - 动作类别数: {self.M}")
        print(f"   - 设备: {self.device}")
        print(f"   - 时间戳ID: {TIMESTAMP_ID}")

    def load_label_map(self) -> Dict[str, str]:
        """加载标签映射"""
        with open(LABEL_MAP_PATH, 'r', encoding='utf-8') as f:
            label_map = json.load(f)
        return label_map

    def load_prerequisites(self) -> Tuple[torch.Tensor, torch.Tensor]:
        """加载预计算的原型和边权重"""
        # 加载原型特征
        prototype_files = [f for f in os.listdir(PROTOTYPE_DIR) if f.startswith('action_prototypes_') and f.endswith('.pt')]
        if not prototype_files:
            raise FileNotFoundError(f"未找到原型文件在 {PROTOTYPE_DIR}")

        latest_prototype_file = sorted(prototype_files)[-1]
        prototype_path = os.path.join(PROTOTYPE_DIR, latest_prototype_file)
        prototypes = torch.load(prototype_path, map_location='cpu')
        print(f"📥 加载原型特征: {prototype_path}")
        print(f"   - 形状: {prototypes.shape}")

        # 加载边权重
        edge_weight_files = [f for f in os.listdir(EDGE_WEIGHT_DIR) if f.startswith('edge_weights_') and f.endswith('.pt')]
        if not edge_weight_files:
            raise FileNotFoundError(f"未找到边权重文件在 {EDGE_WEIGHT_DIR}")

        latest_edge_file = sorted(edge_weight_files)[-1]
        edge_weight_path = os.path.join(EDGE_WEIGHT_DIR, latest_edge_file)
        edge_weights = torch.load(edge_weight_path, map_location='cpu')
        print(f"📥 加载边权重: {edge_weight_path}")
        print(f"   - 形状: {edge_weights.shape}")

        return prototypes, edge_weights

    def create_data_loaders(self, prototypes: torch.Tensor) -> Tuple[DataLoader, DataLoader]:
        """创建数据加载器"""
        print("📊 创建数据加载器...")

        # 创建训练和验证数据集
        train_dataset = DynamicTransitionDataset(TRAIN_DATA_DIR, TRAIN_LABEL_DIR, self.label_map, prototypes, "train")
        val_dataset = DynamicTransitionDataset(TRAIN_DATA_DIR, TRAIN_LABEL_DIR, self.label_map, prototypes, "test")

        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False)

        print(f"   - 训练样本: {len(train_dataset)}")
        print(f"   - 验证样本: {len(val_dataset)}")

        return train_loader, val_loader

    def train_epoch(self, model: DynamicModel, train_loader: DataLoader,
                   optimizer: torch.optim.Optimizer, epoch: int) -> float:
        """训练一个epoch"""
        model.train()
        total_loss = 0.0
        num_batches = 0

        for batch_idx, (features, current_actions, next_actions) in enumerate(train_loader):
            # 移动到设备
            features = features.to(self.device)
            current_actions = current_actions.to(self.device)
            next_actions = next_actions.to(self.device)

            # 前向传播
            logits = model(features, current_actions)

            # 计算损失
            loss = F.cross_entropy(logits, next_actions)

            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            total_loss += loss.item()
            num_batches += 1

            if batch_idx % 100 == 0:
                print(f"   Epoch {epoch}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}")

        avg_loss = total_loss / num_batches if num_batches > 0 else 0.0
        return avg_loss

    def validate_epoch(self, model: DynamicModel, val_loader: DataLoader) -> Tuple[float, float]:
        """验证一个epoch"""
        model.eval()
        total_loss = 0.0
        correct = 0
        total = 0

        with torch.no_grad():
            for features, current_actions, next_actions in val_loader:
                features = features.to(self.device)
                current_actions = current_actions.to(self.device)
                next_actions = next_actions.to(self.device)

                logits = model(features, current_actions)
                loss = F.cross_entropy(logits, next_actions)

                total_loss += loss.item()

                # 计算准确率
                pred = torch.argmax(logits, dim=1)
                correct += (pred == next_actions).sum().item()
                total += next_actions.size(0)

        avg_loss = total_loss / len(val_loader)
        accuracy = correct / total if total > 0 else 0.0

        return avg_loss, accuracy

    def train_model(self, model: DynamicModel, train_loader: DataLoader,
                   val_loader: DataLoader) -> Dict:
        """训练模型"""
        print("🚀 开始训练动态模型...")

        optimizer = torch.optim.Adam(model.mlp_diff.parameters(), lr=LEARNING_RATE)

        # 训练历史
        history = {
            'train_loss': [],
            'val_loss': [],
            'val_accuracy': []
        }

        best_val_loss = float('inf')
        best_model_state = None

        for epoch in range(MAX_EPOCHS):
            print(f"\n📈 Epoch {epoch + 1}/{MAX_EPOCHS}")

            # 训练
            train_loss = self.train_epoch(model, train_loader, optimizer, epoch + 1)

            # 验证
            val_loss, val_accuracy = self.validate_epoch(model, val_loader)

            # 记录历史
            history['train_loss'].append(train_loss)
            history['val_loss'].append(val_loss)
            history['val_accuracy'].append(val_accuracy)

            print(f"   训练损失: {train_loss:.4f}")
            print(f"   验证损失: {val_loss:.4f}")
            print(f"   验证准确率: {val_accuracy:.4f}")

            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                best_model_state = model.state_dict().copy()
                print(f"   ✅ 新的最佳模型 (验证损失: {val_loss:.4f})")

        # 加载最佳模型
        if best_model_state is not None:
            model.load_state_dict(best_model_state)

        print(f"\n✅ 训练完成! 最佳验证损失: {best_val_loss:.4f}")

        return history

    def save_results(self, model: DynamicModel, history: Dict):
        """保存训练结果"""
        print("💾 保存训练结果...")

        # 1. 保存MLP_diff模型（完整state_dict）
        model_path = os.path.join(MODEL_PARAMS_DIR, f"dynamic_model_mlp_{TIMESTAMP_ID}.pt")
        torch.save(model.state_dict(), model_path)
        print(f"   ✅ 动态模型已保存: {model_path}")

        # 2. 保存完整模型信息
        full_model_path = os.path.join(MODEL_PARAMS_DIR, f"dynamic_model_full_{TIMESTAMP_ID}.pt")
        torch.save({
            'model_state_dict': model.state_dict(),
            'mlp_diff_state_dict': model.mlp_diff.state_dict(),
            'edge_weights': model.edge_weights,
            'prototypes': model.prototypes,
            'num_classes': self.M,
            'feature_dim': FEATURE_DIM,
            'hidden_dim': HIDDEN_DIM,
            'alpha': ALPHA
        }, full_model_path)
        print(f"   ✅ 完整模型信息已保存: {full_model_path}")

        # 3. 保存原始数据
        raw_data = {
            'training_history': history,
            'mlp_diff_state_dict': model.mlp_diff.state_dict(),
            'edge_weights': model.edge_weights,
            'prototypes': model.prototypes,
            'metadata': {
                'timestamp_id': TIMESTAMP_ID,
                'num_classes': self.M,
                'feature_dim': FEATURE_DIM,
                'hidden_dim': HIDDEN_DIM,
                'alpha': ALPHA,
                'max_epochs': MAX_EPOCHS,
                'batch_size': BATCH_SIZE,
                'learning_rate': LEARNING_RATE,
                'random_seed': RANDOM_SEED,
                'label_map': self.label_map
            }
        }

        raw_data_path = os.path.join(RAW_DATA_DIR, f"dynamic_model_raw_data_{TIMESTAMP_ID}.pt")
        torch.save(raw_data, raw_data_path)

        # 保存训练历史为JSON
        history_path = os.path.join(RAW_DATA_DIR, f"dynamic_training_history_{TIMESTAMP_ID}.json")
        with open(history_path, 'w', encoding='utf-8') as f:
            json.dump(history, f, indent=2)

        print(f"   ✅ 原始数据已保存: {raw_data_path}")
        print(f"   ✅ 训练历史已保存: {history_path}")

    def generate_visualizations(self, history: Dict):
        """生成可视化图表"""
        print("📊 生成可视化图表...")

        # 1. 训练/验证损失曲线
        plt.figure(figsize=(12, 5))

        plt.subplot(1, 2, 1)
        epochs = range(1, len(history['train_loss']) + 1)
        plt.plot(epochs, history['train_loss'], 'b-', label='训练损失')
        plt.plot(epochs, history['val_loss'], 'r-', label='验证损失')
        plt.xlabel('Epoch')
        plt.ylabel('损失')
        plt.title('训练/验证损失曲线')
        plt.legend()
        plt.grid(True)

        # 2. 验证准确率曲线
        plt.subplot(1, 2, 2)
        plt.plot(epochs, history['val_accuracy'], 'g-', label='验证准确率')
        plt.xlabel('Epoch')
        plt.ylabel('准确率')
        plt.title('验证准确率曲线')
        plt.legend()
        plt.grid(True)

        plt.tight_layout()
        loss_curve_path = os.path.join(VISUALIZATION_DIR, f"training_curves_{TIMESTAMP_ID}.png")
        plt.savefig(loss_curve_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"   ✅ 可视化图表已保存到: {VISUALIZATION_DIR}")

    def run_training(self):
        """执行完整的训练流程"""
        print("=" * 80)
        print("🚀 动态任务图训练开始")
        print("=" * 80)

        # 1. 加载预计算数据
        prototypes, edge_weights = self.load_prerequisites()

        # 2. 创建数据加载器
        train_loader, val_loader = self.create_data_loaders(prototypes)

        # 3. 创建模型
        model = DynamicModel(edge_weights, prototypes, FEATURE_DIM, self.M, HIDDEN_DIM, ALPHA).to(self.device)

        print(f"🧠 模型参数统计:")
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"   - 总参数: {total_params:,}")
        print(f"   - 可训练参数: {trainable_params:,}")

        # 4. 训练模型
        history = self.train_model(model, train_loader, val_loader)

        # 5. 保存结果
        self.save_results(model, history)

        # 6. 生成可视化
        self.generate_visualizations(history)

        print("=" * 80)
        print("✅ 动态任务图训练完成!")
        print(f"📁 输出目录: {OUTPUT_BASE}")
        print(f"🏷️ 时间戳ID: {TIMESTAMP_ID}")
        print("=" * 80)


def main():
    """主函数"""
    trainer = DynamicModelTrainer()
    trainer.run_training()


if __name__ == "__main__":
    main()
