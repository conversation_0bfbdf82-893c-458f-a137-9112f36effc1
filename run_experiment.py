#!/usr/bin/env python
"""
动态任务图谱差分更新实验 - 完整运行脚本

使用方法:
    python run_experiment.py --config configs/config.yaml --name my_experiment

    或者使用默认配置:
    python run_experiment.py
"""

import argparse
import sys
import os
from pathlib import Path
import yaml
import torch

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="动态任务图谱差分更新实验",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument(
        '--config',
        type=str,
        default='configs/config.yaml',
        help='配置文件路径'
    )

    parser.add_argument(
        '--name',
        type=str,
        default=None,
        help='实验名称（可选）'
    )

    parser.add_argument(
        '--test',
        action='store_true',
        help='运行测试模式'
    )

    return parser.parse_args()

def validate_config(config_path: str) -> bool:
    """验证配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # 检查必需的配置项
        required_sections = ['data', 'output', 'training', 'model', 'evaluation']
        for section in required_sections:
            if section not in config:
                print(f"错误: 配置文件缺少必需的节: {section}")
                return False

        return True

    except Exception as e:
        print(f"配置文件验证失败: {e}")
        return False

def test_core_functionality():
    """测试核心功能"""
    print("\n" + "="*60)
    print("测试核心功能")
    print("="*60)
    
    # 测试导入
    try:
        from src.models import BaseModel, MLPDiff, StaticModel, DynamicModel
        print("✅ 模型模块导入成功")
    except ImportError as e:
        print(f"❌ 模型模块导入失败: {e}")
        return False
    
    try:
        from src.utils.data_loader import BreakfastDataLoader
        from src.utils.evaluation_metrics import EvaluationMetrics
        from src.utils.visualization import VisualizationManager
        from src.utils.file_utils import FileManager
        print("✅ 工具模块导入成功")
    except ImportError as e:
        print(f"❌ 工具模块导入失败: {e}")
        return False
    
    # 测试GPU
    if torch.cuda.is_available():
        print(f"✅ GPU可用: {torch.cuda.device_count()}个设备")
    else:
        print("⚠️  GPU不可用，将使用CPU")
    
    return True

def main():
    """主函数"""
    args = parse_arguments()
    
    print("=" * 60)
    print("动态任务图谱差分更新实验")
    print("=" * 60)
    
    # 验证配置文件
    if not validate_config(args.config):
        sys.exit(1)
    
    # 如果是测试模式
    if args.test:
        if test_core_functionality():
            print("\n✅ 核心功能测试通过")
        else:
            print("\n❌ 核心功能测试失败")
            sys.exit(1)
        return
    
    # 加载配置
    with open(args.config, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    print(f"\n实验配置:")
    print(f"- 训练数据: {config['training']['train_splits']}")
    print(f"- 测试数据: {config['training']['test_splits']}")
    print(f"- 批次大小: {config['training']['batch_size']}")
    print(f"- 最大轮数: {config['training']['max_epochs']}")
    
    print("\n实验即将开始...")
    print("(完整实验功能将在后续实现)")
    
    # TODO: 实现完整的实验流程
    # 1. 数据预处理 (txt_to_npy.py)
    # 2. 训练原型特征 (Train_Action_Prototype.py)
    # 3. 训练边权重 (Train_Edge_Weight.py)
    # 4. 训练静态模型 (Train_Static_Model.py)
    # 5. 训练动态模型 (Train_Dynamic_Model.py)
    # 6. 模型评估 (Test_Static_vs_Dynamic.py)

if __name__ == "__main__":
    main()
