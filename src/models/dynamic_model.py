"""动态模型定义"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from .base_model import BaseModel
from .mlp_diff import MLPDiff
from typing import Dict

class DynamicModel(BaseModel):
    """动态任务图模型"""
    
    def __init__(self, edge_weights: torch.Tensor, prototypes: torch.Tensor, config: Dict):
        super().__init__(config)
        
        self.num_classes = edge_weights.size(0)
        self.feature_dim = prototypes.size(1)
        
        # 冻结的组件
        self.register_buffer('edge_weights', edge_weights)
        self.register_buffer('prototypes', prototypes)
        
        # 可学习的差分网络
        self.mlp_diff = MLPDiff(config, num_classes=self.num_classes)
        
        # 可选的门控机制
        self.use_gating = config['model'].get('use_gating', False)
        if self.use_gating:
            self.gate = nn.Sequential(
                nn.Linear(self.feature_dim, 1),
                nn.Sigmoid()
            )
        
        # 可选的残差连接权重
        self.residual_weight = nn.Parameter(torch.tensor(config['model'].get('residual_weight_init', 0.1)))
    
    def compute_diff(self, features: torch.Tensor, current_actions: torch.Tensor) -> torch.Tensor:
        """计算特征差分"""
        # 获取对应的原型特征
        prototype_features = self.prototypes[current_actions]  # [batch_size, feature_dim]
        
        # 计算绝对差值
        diff = torch.abs(features - prototype_features)
        
        return diff
    
    def forward(self, features: torch.Tensor, current_actions: torch.Tensor) -> torch.Tensor:
        """前向传播
        
        Args:
            features: 当前帧特征 [batch_size, feature_dim]
            current_actions: 当前动作索引 [batch_size]
            
        Returns:
            logits: 调整后的下一动作预测logits [batch_size, num_classes]
        """
        # 计算特征差分
        diff = self.compute_diff(features, current_actions)
        
        # 通过MLP_diff计算权重调整量
        delta_weights = self.mlp_diff(diff)
        
        # 获取静态边权重
        static_logits = self.edge_weights[current_actions]
        
        # 可选的门控机制
        if self.use_gating:
            gate_value = self.gate(diff)
            delta_weights = delta_weights * gate_value
        
        # 组合静态权重和动态调整
        logits = static_logits + self.residual_weight * delta_weights
        
        return logits
    
    def get_adaptation_info(self, features: torch.Tensor, current_actions: torch.Tensor) -> Dict:
        """获取适应性调整的详细信息（用于分析）"""
        diff = self.compute_diff(features, current_actions)
        delta_weights = self.mlp_diff(diff)
        
        info = {
            'diff_magnitude': torch.norm(diff, dim=-1),
            'delta_weights': delta_weights,
            'max_adjustment': torch.max(torch.abs(delta_weights), dim=-1)[0]
        }
        
        if self.use_gating:
            info['gate_values'] = self.gate(diff).squeeze(-1)
        
        return info
