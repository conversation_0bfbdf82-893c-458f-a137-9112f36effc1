"""基础训练器类 - 提供统一的训练接口和文件管理"""
import os
import sys
import torch
import numpy as np
import json
import yaml
from pathlib import Path
from datetime import datetime
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.file_utils import FileManager


class BaseTrainer(ABC):
    """基础训练器抽象类 - 提供统一的时间戳ID和文件管理"""
    
    def __init__(self, config: Dict, experiment_id: str = None, file_manager: FileManager = None):
        """
        Args:
            config: 配置字典
            experiment_id: 实验ID（时间戳），如果为None则自动生成
            file_manager: 文件管理器，如果为None则创建新的
        """
        self.config = config
        
        # 使用提供的实验ID或生成新的
        if experiment_id is None:
            self.experiment_id = datetime.now().strftime("%Y%m%d-%H%M%S")
        else:
            self.experiment_id = experiment_id
            
        # 使用提供的文件管理器或创建新的
        if file_manager is None:
            self.file_manager = FileManager(
                config['output']['base_path'],
                experiment_name=self.experiment_id
            )
        else:
            self.file_manager = file_manager
            
        # 设置设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 设置随机种子
        self._set_random_seeds(config['training']['random_seed'])
        
        # 创建输出目录（根据IDEA.md规范，直接使用base_path + 训练器名称）
        base_output_path = Path(config['output']['base_path'])
        trainer_name = self.get_trainer_name()
        self.output_dir = base_output_path / trainer_name
        self.model_params_dir = self.output_dir / "Model_parameters"
        self.raw_data_dir = self.output_dir / "Raw_data"
        self.visualization_dir = self.output_dir / "Visualization"
        
        # 创建目录
        self.model_params_dir.mkdir(parents=True, exist_ok=True)
        self.raw_data_dir.mkdir(parents=True, exist_ok=True)
        self.visualization_dir.mkdir(parents=True, exist_ok=True)
        
        self.file_manager.logger.info(f"初始化 {self.get_trainer_name()}")
        self.file_manager.logger.info(f"实验ID: {self.experiment_id}")
        self.file_manager.logger.info(f"设备: {self.device}")
        
    def _set_random_seeds(self, seed: int):
        """设置随机种子"""
        np.random.seed(seed)
        torch.manual_seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed_all(seed)
            torch.backends.cudnn.deterministic = True
            torch.backends.cudnn.benchmark = False
    
    @abstractmethod
    def get_trainer_name(self) -> str:
        """获取训练器名称，用于创建输出目录"""
        pass
    
    @abstractmethod
    def train(self) -> Dict[str, Any]:
        """执行训练，返回训练结果"""
        pass
    
    def save_model_parameters(self, parameters: Any, filename_prefix: str):
        """保存模型参数"""
        filepath = self.model_params_dir / f"{filename_prefix}_{self.experiment_id}.pt"
        torch.save(parameters, filepath)
        self.file_manager.logger.info(f"模型参数已保存: {filepath}")
        return filepath
    
    def save_raw_data(self, data: Dict, filename_prefix: str):
        """保存原始数据"""
        filepath = self.raw_data_dir / f"{filename_prefix}_{self.experiment_id}.pt"
        torch.save(data, filepath)
        
        # 如果数据中包含统计信息，同时保存为JSON
        if 'statistics' in data or 'metadata' in data:
            json_filepath = self.raw_data_dir / f"{filename_prefix}_{self.experiment_id}.json"
            json_data = {}
            
            # 转换不可序列化的对象
            for key, value in data.items():
                if key in ['statistics', 'metadata']:
                    # 递归处理嵌套字典，转换tuple键为字符串
                    if isinstance(value, dict):
                        converted_value = {}
                        for k, v in value.items():
                            # 将tuple键转换为字符串
                            if isinstance(k, tuple):
                                k = str(k)
                            converted_value[k] = v
                        json_data[key] = converted_value
                    else:
                        json_data[key] = value
                elif isinstance(value, (dict, list, str, int, float)):
                    json_data[key] = value
                    
            with open(json_filepath, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)
                
        self.file_manager.logger.info(f"原始数据已保存: {filepath}")
        return filepath
    
    def log_to_memory(self, log_type: str, message: str, details: Dict = None):
        """记录到Memory日志"""
        self.file_manager.log_to_memory(log_type, message, details)
        
    def load_label_map(self) -> Dict[str, str]:
        """加载标签映射"""
        label_map_path = Path(self.config['data']['label_map_path'])
        if not label_map_path.exists():
            raise FileNotFoundError(f"标签映射文件不存在: {label_map_path}")
            
        with open(label_map_path, 'r', encoding='utf-8') as f:
            label_map = json.load(f)
            
        self.file_manager.logger.info(f"加载标签映射: {len(label_map)} 个动作类别")
        return label_map 