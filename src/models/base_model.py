"""基础模型类"""
import torch
import torch.nn as nn
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import gc

class BaseModel(nn.Module, ABC):
    """基础模型抽象类"""
    
    def __init__(self, config: Dict):
        super().__init__()
        self.config = config
        self.device = None
        self._memory_efficient = config.get('hardware', {}).get('memory_efficient', True)
        self._enable_amp = config.get('hardware', {}).get('mixed_precision', False)
    
    @abstractmethod
    def forward(self, *args, **kwargs):
        pass
    
    def save_model(self, filepath: str):
        """保存模型"""
        # 移到CPU保存以减少GPU内存占用
        state_dict = {k: v.cpu() for k, v in self.state_dict().items()}
        torch.save(state_dict, filepath)
    
    def load_model(self, filepath: str, device: Optional[torch.device] = None):
        """加载模型"""
        # 直接加载到指定设备
        if device is None:
            device = self.device if self.device is not None else torch.device('cpu')
        
        state_dict = torch.load(filepath, map_location=device)
        self.load_state_dict(state_dict)
        self.to(device)
    
    def to(self, device):
        """重写to方法以跟踪设备"""
        self.device = device
        return super().to(device)
    
    def get_num_parameters(self) -> Dict[str, int]:
        """获取模型参数统计"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        # 计算内存占用（MB）
        param_memory = sum(p.numel() * p.element_size() for p in self.parameters()) / (1024**2)
        buffer_memory = sum(b.numel() * b.element_size() for b in self.buffers()) / (1024**2)
        
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'frozen_parameters': total_params - trainable_params,
            'param_memory_mb': param_memory,
            'buffer_memory_mb': buffer_memory,
            'total_memory_mb': param_memory + buffer_memory
        }
    
    def freeze_parameters(self, parameter_names: list = None):
        """冻结指定参数"""
        if parameter_names is None:
            # 冻结所有参数
            for param in self.parameters():
                param.requires_grad = False
        else:
            # 冻结指定参数
            for name, param in self.named_parameters():
                if any(pname in name for pname in parameter_names):
                    param.requires_grad = False
    
    def unfreeze_parameters(self, parameter_names: list = None):
        """解冻指定参数"""
        if parameter_names is None:
            # 解冻所有参数
            for param in self.parameters():
                param.requires_grad = True
        else:
            # 解冻指定参数
            for name, param in self.named_parameters():
                if any(pname in name for pname in parameter_names):
                    param.requires_grad = True
    
    def get_gpu_memory_usage(self) -> Dict[str, float]:
        """获取GPU内存使用情况"""
        if not torch.cuda.is_available() or self.device.type != 'cuda':
            return {}
        
        device_id = self.device.index if self.device.index is not None else 0
        
        return {
            'allocated_mb': torch.cuda.memory_allocated(device_id) / (1024**2),
            'reserved_mb': torch.cuda.memory_reserved(device_id) / (1024**2),
            'max_allocated_mb': torch.cuda.max_memory_allocated(device_id) / (1024**2),
            'available_mb': (torch.cuda.get_device_properties(device_id).total_memory - 
                           torch.cuda.memory_allocated(device_id)) / (1024**2)
        }
    
    def clear_cache(self):
        """清理GPU缓存"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            gc.collect()
    
    def enable_gradient_checkpointing(self):
        """启用梯度检查点以节省内存（子类可重写）"""
        pass
    
    def set_memory_efficient_mode(self, enable: bool = True):
        """设置内存高效模式"""
        self._memory_efficient = enable
        if enable:
            torch.backends.cudnn.benchmark = False
            torch.backends.cudnn.deterministic = True
        else:
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
