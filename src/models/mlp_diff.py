"""MLP_diff网络定义"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from .base_model import BaseModel
from typing import List, Dict, Optional

class MLPDiff(BaseModel):
    """差分MLP网络 - 优化版本"""
    
    def __init__(self, config: Dict, num_classes: Optional[int] = None):
        super().__init__(config)
        
        # 网络参数 - 确保符合IDEA.md规范
        input_dim = config['model']['feature_dim']  # 64 (视觉特征维度)
        hidden_dims = config['model']['mlp_hidden_dims']  # [512, 256] - IDEA.md指定的隐藏层维度
        
        # num_classes可以从参数传入，或从config中获取（如果存在）
        if num_classes is not None:
            output_dim = num_classes
        else:
            output_dim = config['model'].get('num_classes', 48)  # 默认48个类别
            
        self.num_classes = output_dim
        dropout_rate = config['model'].get('dropout_rate', 0.1)
        activation = config['model'].get('activation', 'relu')  # IDEA.md指定使用ReLU
        
        # 构建网络层
        layers = []
        prev_dim = input_dim
        
        # 输入层归一化
        layers.append(nn.LayerNorm(input_dim))
        
        # 隐藏层
        for i, hidden_dim in enumerate(hidden_dims):
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                self._get_activation(activation),
                nn.Dropout(dropout_rate),
                nn.LayerNorm(hidden_dim)  # 层归一化提升稳定性
            ])
            prev_dim = hidden_dim
        
        # 输出层（不使用激活函数，因为输出是logits调整量）
        layers.append(nn.Linear(prev_dim, output_dim))
        
        self.mlp = nn.Sequential(*layers)
        
        # 权重初始化
        self._initialize_weights()
    
    def _get_activation(self, activation: str) -> nn.Module:
        """获取激活函数"""
        activations = {
            'relu': nn.ReLU(),
            'gelu': nn.GELU(),
            'leaky_relu': nn.LeakyReLU(0.1),
            'swish': nn.SiLU()
        }
        return activations.get(activation, nn.ReLU())
    
    def _initialize_weights(self):
        """权重初始化"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, diff_features: torch.Tensor) -> torch.Tensor:
        """前向传播
        
        Args:
            diff_features: 差分特征 [batch_size, feature_dim]
            
        Returns:
            delta_weights: 权重调整量 [batch_size, num_classes]
        """
        return self.mlp(diff_features)
