"""静态模型定义"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from .base_model import BaseModel
from typing import Dict

class StaticModel(BaseModel):
    """静态任务图模型"""
    
    def __init__(self, edge_weights: torch.Tensor, config: Dict = None):
        # 如果没有提供config，创建一个默认的
        if config is None:
            config = {'model': {}}
        
        super().__init__(config)
        
        self.num_classes = edge_weights.size(0)
        
        # 冻结的边权重矩阵
        self.register_buffer('edge_weights', edge_weights)
        
        # 可学习的偏置向量
        self.bias = nn.Parameter(torch.zeros(self.num_classes))
        
        # 可选的温度参数用于调节输出分布
        self.temperature = nn.Parameter(torch.ones(1))
        
        # 配置
        self.use_temperature = config['model'].get('use_temperature', False)
    
    def forward(self, current_actions: torch.Tensor) -> torch.Tensor:
        """前向传播
        
        Args:
            current_actions: 当前动作索引 [batch_size]
            
        Returns:
            logits: 下一动作的预测logits [batch_size, num_classes]
        """
        # 根据当前动作索引选取对应的边权重行
        batch_size = current_actions.size(0)
        logits = self.edge_weights[current_actions] + self.bias.unsqueeze(0)
        
        # 可选的温度缩放
        if self.use_temperature:
            logits = logits / self.temperature
        
        return logits
    
    def get_transition_probabilities(self, current_actions: torch.Tensor) -> torch.Tensor:
        """获取转移概率分布"""
        logits = self.forward(current_actions)
        return F.softmax(logits, dim=-1)
