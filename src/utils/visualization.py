"""可视化工具"""
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from pathlib import Path

class VisualizationManager:
    """可视化管理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        
        # 设置样式
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # 可视化参数
        self.dpi = config['visualization']['dpi']
        self.figsize = tuple(config['visualization']['figsize'])
        self.save_format = config['visualization'].get('save_format', 'png')
        
        # 颜色配置
        self.colors = {
            'train': '#2E86AB',
            'val': '#A23B72',
            'test': '#F18F01',
            'static': '#C73E1D',
            'dynamic': '#1B998B'
        }
    
    def plot_training_curves(self, train_losses: List[float], 
                           val_losses: List[float], 
                           save_path: str,
                           title: str = "Training Curves"):
        """绘制训练损失曲线"""
        fig, ax = plt.subplots(figsize=self.figsize, dpi=self.dpi)
        
        epochs = range(1, len(train_losses) + 1)
        
        ax.plot(epochs, train_losses, 'o-', color=self.colors['train'], 
                label='Training Loss', linewidth=2, markersize=4)
        
        if val_losses:
            ax.plot(epochs, val_losses, 's-', color=self.colors['val'], 
                    label='Validation Loss', linewidth=2, markersize=4)
        
        ax.set_xlabel('Epoch', fontsize=12)
        ax.set_ylabel('Loss', fontsize=12)
        ax.set_title(title, fontsize=14, fontweight='bold')
        ax.legend(fontsize=10)
        ax.grid(True, alpha=0.3)
        
        # 添加最小值标注
        min_train_idx = np.argmin(train_losses)
        ax.annotate(f'Min: {train_losses[min_train_idx]:.4f}', 
                   xy=(min_train_idx + 1, train_losses[min_train_idx]),
                   xytext=(10, 10), textcoords='offset points',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                   arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
    
    def plot_confusion_matrix(self, y_true: np.ndarray, 
                            y_pred: np.ndarray, 
                            class_names: List[str], 
                            save_path: str,
                            normalize: bool = True):
        """绘制混淆矩阵"""
        from sklearn.metrics import confusion_matrix
        
        cm = confusion_matrix(y_true, y_pred)
        
        if normalize:
            cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
            fmt = '.2f'
            title = 'Normalized Confusion Matrix'
        else:
            fmt = 'd'
            title = 'Confusion Matrix'
        
        fig, ax = plt.subplots(figsize=(10, 8), dpi=self.dpi)
        
        sns.heatmap(cm, annot=True, fmt=fmt, cmap='Blues', 
                   xticklabels=class_names, yticklabels=class_names,
                   ax=ax, cbar_kws={'shrink': 0.8})
        
        ax.set_xlabel('Predicted Label', fontsize=12)
        ax.set_ylabel('True Label', fontsize=12)
        ax.set_title(title, fontsize=14, fontweight='bold')
        
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        plt.tight_layout()
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
    
    def plot_transition_matrix(self, transition_matrix: np.ndarray, 
                             action_names: List[str], 
                             save_path: str,
                             title: str = "Action Transition Matrix"):
        """绘制转移矩阵热力图"""
        fig, ax = plt.subplots(figsize=(12, 10), dpi=self.dpi)
        
        # 使用对数尺度以更好地显示差异
        log_matrix = np.log(transition_matrix + 1e-8)
        
        sns.heatmap(log_matrix, annot=True, fmt='.2f', cmap='viridis',
                   xticklabels=action_names, yticklabels=action_names,
                   ax=ax, cbar_kws={'label': 'Log Probability'})
        
        ax.set_xlabel('Next Action', fontsize=12)
        ax.set_ylabel('Current Action', fontsize=12)
        ax.set_title(title, fontsize=14, fontweight='bold')
        
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        plt.tight_layout()
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
    
    def plot_prototype_statistics(self, prototype_stats: Dict, 
                                save_path: str):
        """绘制原型特征统计图"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12), dpi=self.dpi)
        
        actions = list(prototype_stats['sample_counts'].keys())
        sample_counts = list(prototype_stats['sample_counts'].values())
        variances = list(prototype_stats['variances'].values())
        
        # 样本数量分布
        bars1 = ax1.bar(actions, sample_counts, color=self.colors['train'], alpha=0.7)
        ax1.set_xlabel('Action Class')
        ax1.set_ylabel('Sample Count')
        ax1.set_title('Sample Count per Action Class')
        ax1.tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar, count in zip(bars1, sample_counts):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01*max(sample_counts),
                    str(count), ha='center', va='bottom')
        
        # 方差分布
        bars2 = ax2.bar(actions, variances, color=self.colors['val'], alpha=0.7)
        ax2.set_xlabel('Action Class')
        ax2.set_ylabel('Feature Variance')
        ax2.set_title('Feature Variance per Action Class')
        ax2.tick_params(axis='x', rotation=45)
        
        # 样本数量vs方差散点图
        ax3.scatter(sample_counts, variances, c=range(len(actions)), 
                   cmap='viridis', s=100, alpha=0.7)
        ax3.set_xlabel('Sample Count')
        ax3.set_ylabel('Feature Variance')
        ax3.set_title('Sample Count vs Feature Variance')
        
        # 添加动作标签
        for i, action in enumerate(actions):
            ax3.annotate(action, (sample_counts[i], variances[i]),
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        # 特征维度方差分布（如果有的话）
        if 'dimension_variances' in prototype_stats:
            dim_vars = prototype_stats['dimension_variances']
            ax4.plot(range(len(dim_vars)), dim_vars, 'o-', color=self.colors['test'])
            ax4.set_xlabel('Feature Dimension')
            ax4.set_ylabel('Average Variance')
            ax4.set_title('Variance across Feature Dimensions')
            ax4.grid(True, alpha=0.3)
        else:
            ax4.text(0.5, 0.5, 'No dimension variance data', 
                    ha='center', va='center', transform=ax4.transAxes)
            ax4.set_title('Feature Dimension Analysis')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
    
    def plot_comparison_results(self, static_results: Dict, 
                              dynamic_results: Dict, 
                              save_path: str):
        """绘制静态vs动态模型对比图"""
        # 提取指标名称和值
        metrics = []
        static_values = []
        dynamic_values = []
        static_ci_lower = []
        static_ci_upper = []
        dynamic_ci_lower = []
        dynamic_ci_upper = []
        
        for metric_name in static_results['summary']:
            if metric_name in dynamic_results['summary']:
                metrics.append(metric_name)
                
                static_data = static_results['summary'][metric_name]
                dynamic_data = dynamic_results['summary'][metric_name]
                
                static_values.append(static_data['mean'])
                dynamic_values.append(dynamic_data['mean'])
                static_ci_lower.append(static_data['ci_lower'])
                static_ci_upper.append(static_data['ci_upper'])
                dynamic_ci_lower.append(dynamic_data['ci_lower'])
                dynamic_ci_upper.append(dynamic_data['ci_upper'])
        
        # 创建对比图
        fig, ax = plt.subplots(figsize=(15, 8), dpi=self.dpi)
        
        x = np.arange(len(metrics))
        width = 0.35
        
        # 计算误差条
        static_errors = [np.array(static_values) - np.array(static_ci_lower),
                        np.array(static_ci_upper) - np.array(static_values)]
        dynamic_errors = [np.array(dynamic_values) - np.array(dynamic_ci_lower),
                         np.array(dynamic_ci_upper) - np.array(dynamic_values)]
        
        # 绘制柱状图
        bars1 = ax.bar(x - width/2, static_values, width, 
                      label='Static Model', color=self.colors['static'], 
                      alpha=0.8, yerr=static_errors, capsize=5)
        bars2 = ax.bar(x + width/2, dynamic_values, width,
                      label='Dynamic Model', color=self.colors['dynamic'], 
                      alpha=0.8, yerr=dynamic_errors, capsize=5)
        
        ax.set_xlabel('Metrics', fontsize=12)
        ax.set_ylabel('Score', fontsize=12)
        ax.set_title('Static vs Dynamic Model Performance Comparison', 
                    fontsize=14, fontweight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels(metrics, rotation=45, ha='right')
        ax.legend()
        ax.grid(True, alpha=0.3, axis='y')
        
        # 添加数值标签
        for bar, value in zip(bars1, static_values):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                   f'{value:.3f}', ha='center', va='bottom', fontsize=8)
        
        for bar, value in zip(bars2, dynamic_values):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                   f'{value:.3f}', ha='center', va='bottom', fontsize=8)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
    
    def plot_dynamic_adaptation_analysis(self, adaptation_data: Dict, 
                                       save_path: str):
        """绘制动态适应性分析图"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12), dpi=self.dpi)
        
        # 差分幅度分布
        diff_magnitudes = adaptation_data['diff_magnitudes']
        ax1.hist(diff_magnitudes, bins=50, alpha=0.7, color=self.colors['train'])
        ax1.set_xlabel('Diff Magnitude')
        ax1.set_ylabel('Frequency')
        ax1.set_title('Distribution of Feature Differences')
        ax1.grid(True, alpha=0.3)
        
        # 调整量幅度分布
        adjustment_magnitudes = adaptation_data['adjustment_magnitudes']
        ax2.hist(adjustment_magnitudes, bins=50, alpha=0.7, color=self.colors['val'])
        ax2.set_xlabel('Adjustment Magnitude')
        ax2.set_ylabel('Frequency')
        ax2.set_title('Distribution of Weight Adjustments')
        ax2.grid(True, alpha=0.3)
        
        # 差分vs调整量散点图
        ax3.scatter(diff_magnitudes, adjustment_magnitudes, alpha=0.5, 
                   c=self.colors['test'], s=20)
        ax3.set_xlabel('Diff Magnitude')
        ax3.set_ylabel('Adjustment Magnitude')
        ax3.set_title('Feature Diff vs Weight Adjustment')
        ax3.grid(True, alpha=0.3)
        
        # 添加趋势线
        z = np.polyfit(diff_magnitudes, adjustment_magnitudes, 1)
        p = np.poly1d(z)
        ax3.plot(sorted(diff_magnitudes), p(sorted(diff_magnitudes)), 
                "r--", alpha=0.8, linewidth=2)
        
        # 按动作类别的适应性分析
        if 'action_adaptations' in adaptation_data:
            action_data = adaptation_data['action_adaptations']
            actions = list(action_data.keys())
            avg_adjustments = [np.mean(action_data[action]) for action in actions]
            
            bars = ax4.bar(actions, avg_adjustments, color=self.colors['dynamic'], alpha=0.7)
            ax4.set_xlabel('Action Class')
            ax4.set_ylabel('Average Adjustment Magnitude')
            ax4.set_title('Average Adaptation by Action Class')
            ax4.tick_params(axis='x', rotation=45)
            
            # 添加数值标签
            for bar, value in zip(bars, avg_adjustments):
                ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                        f'{value:.3f}', ha='center', va='bottom', fontsize=8)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
