"""
学术报告生成模块

生成符合学术发表标准的报告和可视化图表
支持动态vs静态方法对比、统计显著性测试等
"""

import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
from scipy import stats
from typing import Dict, List, Tuple, Optional
import os
from pathlib import Path


# 设置学术质量的图表样式
plt.rcParams.update({
    'font.size': 12,
    'font.family': 'serif',
    'axes.linewidth': 1.2,
    'xtick.major.size': 5,
    'ytick.major.size': 5,
    'legend.frameon': False,
    'figure.figsize': (6.4, 4.8),
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight'
})

# 配色方案
COLORS = {
    'dynamic': '#2E86AB',
    'static': '#A23B2E',
    'neutral': '#F18F01'
}


class AcademicReportGenerator:
    """学术报告生成器"""
    
    def __init__(self, output_dir: str):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        (self.output_dir / "figures").mkdir(exist_ok=True)
        (self.output_dir / "tables").mkdir(exist_ok=True)
        (self.output_dir / "reports").mkdir(exist_ok=True)
    
    def generate_performance_comparison(self, 
                                      dynamic_results: Dict[str, float],
                                      static_results: Dict[str, float]) -> None:
        """生成动态vs静态方法性能对比图"""
        metrics = ['IoU', 'F1-Score', 'Accuracy']
        methods = ['Dynamic', 'Static']
        
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        for i, metric in enumerate(metrics):
            metric_key = metric.lower().replace('-', '_')
            values = [
                dynamic_results.get(metric_key, 0),
                static_results.get(metric_key, 0)
            ]
            
            bars = axes[i].bar(methods, values, 
                             color=[COLORS['dynamic'], COLORS['static']],
                             alpha=0.8, edgecolor='black')
            
            # 添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                axes[i].text(bar.get_x() + bar.get_width()/2., height + 0.01,
                           f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
            
            axes[i].set_title(f'{metric} Comparison', fontweight='bold')
            axes[i].set_ylabel(metric)
            axes[i].set_ylim(0, max(values) * 1.2)
            axes[i].grid(True, alpha=0.3, axis='y')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / "figures" / "performance_comparison.png")
        plt.savefig(self.output_dir / "figures" / "performance_comparison.pdf")
        plt.close()
    
    def generate_training_curves(self, 
                                train_losses: List[float],
                                val_losses: List[float],
                                metrics_data: Dict[str, Dict[str, List[float]]]) -> None:
        """生成训练曲线图"""
        epochs = range(1, len(train_losses) + 1)
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        
        # 损失曲线
        axes[0,0].plot(epochs, train_losses, 'o-', color=COLORS['dynamic'], 
                      label='Training', linewidth=2)
        axes[0,0].plot(epochs, val_losses, 's-', color=COLORS['static'], 
                      label='Validation', linewidth=2)
        axes[0,0].set_title('Training Loss', fontweight='bold')
        axes[0,0].set_xlabel('Epoch')
        axes[0,0].set_ylabel('Loss')
        axes[0,0].legend()
        axes[0,0].grid(True, alpha=0.3)
        
        # 其他指标
        metric_names = ['iou', 'f1', 'accuracy']
        positions = [(0,1), (1,0), (1,1)]
        titles = ['IoU Score', 'F1-Score', 'Accuracy']
        
        for metric, pos, title in zip(metric_names, positions, titles):
            if 'train' in metrics_data and metric in metrics_data['train']:
                axes[pos].plot(epochs, metrics_data['train'][metric], 'o-', 
                             color=COLORS['dynamic'], label='Training', linewidth=2)
            if 'val' in metrics_data and metric in metrics_data['val']:
                axes[pos].plot(epochs, metrics_data['val'][metric], 's-', 
                             color=COLORS['static'], label='Validation', linewidth=2)
            axes[pos].set_title(title, fontweight='bold')
            axes[pos].set_xlabel('Epoch')
            axes[pos].set_ylabel(title.split()[0])
            axes[pos].legend()
            axes[pos].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / "figures" / "training_curves.png")
        plt.savefig(self.output_dir / "figures" / "training_curves.pdf")
        plt.close()
    
    def perform_statistical_tests(self, 
                                 dynamic_scores: List[float],
                                 static_scores: List[float],
                                 metric_name: str) -> Dict[str, float]:
        """执行统计显著性测试"""
        # T检验
        t_stat, t_pvalue = stats.ttest_ind(dynamic_scores, static_scores)
        
        # Mann-Whitney U检验
        try:
            u_stat, u_pvalue = stats.mannwhitneyu(dynamic_scores, static_scores, 
                                                 alternative='two-sided')
        except:
            u_stat, u_pvalue = 0, 1.0
        
        # 效应量 (Cohen's d)
        pooled_std = np.sqrt(((len(dynamic_scores) - 1) * np.var(dynamic_scores, ddof=1) + 
                             (len(static_scores) - 1) * np.var(static_scores, ddof=1)) / 
                            (len(dynamic_scores) + len(static_scores) - 2))
        cohens_d = (np.mean(dynamic_scores) - np.mean(static_scores)) / pooled_std if pooled_std > 0 else 0
        
        return {
            'metric': metric_name,
            'dynamic_mean': np.mean(dynamic_scores),
            'dynamic_std': np.std(dynamic_scores),
            'static_mean': np.mean(static_scores),
            'static_std': np.std(static_scores),
            't_pvalue': t_pvalue,
            'u_pvalue': u_pvalue,
            'cohens_d': cohens_d,
            'is_significant': t_pvalue < 0.05
        }
    
    def generate_full_report(self, 
                           experiment_results: Dict,
                           experiment_config: Dict) -> None:
        """生成完整的学术报告"""
        report_content = f"""
# Plan_Min Dynamic Task Graph Experiment Report

## Experiment Configuration
- **Task**: {experiment_config.get('task', 'cereals')}
- **Dataset**: Breakfast Dataset
- **Feature Dimension**: {experiment_config.get('D', 64)}
- **Action Classes**: {experiment_config.get('M', 5)}
- **Actions**: {', '.join(experiment_config.get('actions', []))}

## Results Summary
The dynamic task graph differential update method was compared against a static baseline method.

### Performance Metrics
"""
        
        if 'dynamic_results' in experiment_results and 'static_results' in experiment_results:
            dynamic = experiment_results['dynamic_results']
            static = experiment_results['static_results']
            
            for metric in ['iou', 'f1', 'accuracy']:
                if metric in dynamic and metric in static:
                    improvement = ((dynamic[metric] - static[metric]) / static[metric] * 100) if static[metric] > 0 else 0
                    report_content += f"- **{metric.upper()}**: Dynamic {dynamic[metric]:.4f} vs Static {static[metric]:.4f} ({improvement:+.2f}%)\n"
        
        report_content += """
## Generated Files
- `figures/performance_comparison.png/pdf`: Performance comparison charts
- `figures/training_curves.png/pdf`: Training progress visualization

---
*Report generated by Plan_Min Academic Report Generator*
"""
        
        # 保存报告
        with open(self.output_dir / "reports" / "experiment_report.md", 'w') as f:
            f.write(report_content)


def create_demo_results() -> Dict:
    """创建演示结果"""
    return {
        'dynamic_results': {'iou': 0.725, 'f1': 0.812, 'accuracy': 0.857},
        'static_results': {'iou': 0.689, 'f1': 0.786, 'accuracy': 0.823},
        'train_losses': [2.1, 1.5, 1.1, 0.8, 0.6],
        'val_losses': [2.3, 1.6, 1.2, 0.9, 0.7],
        'metrics_data': {
            'train': {'iou': [0.4, 0.6, 0.68, 0.71, 0.72], 'f1': [0.5, 0.7, 0.78, 0.81, 0.81], 'accuracy': [0.6, 0.8, 0.84, 0.85, 0.86]},
            'val': {'iou': [0.35, 0.55, 0.63, 0.67, 0.70], 'f1': [0.45, 0.65, 0.73, 0.77, 0.80], 'accuracy': [0.55, 0.75, 0.80, 0.82, 0.85]}
        }
    }


if __name__ == "__main__":
    # 演示用法
    output_dir = "/data2/syd_data/Breakfast_Data/Outputs"
    generator = AcademicReportGenerator(output_dir)
    
    results = create_demo_results()
    config = {
        'task': 'cereals', 'D': 64, 'M': 5,
        'actions': ['pour_cereals', 'pour_milk', 'SIL', 'stir_cereals', 'take_bowl']
    }
    
    generator.generate_performance_comparison(results['dynamic_results'], results['static_results'])
    generator.generate_training_curves(results['train_losses'], results['val_losses'], results['metrics_data'])
    generator.generate_full_report(results, config)
    
    print("Academic report generated successfully!") 