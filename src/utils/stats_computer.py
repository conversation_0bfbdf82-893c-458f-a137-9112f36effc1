"""
离线统计计算模块

计算并保存动作原型(prototypes)和初始图权重(W0)
"""

import torch
import numpy as np
from collections import defaultdict
import os
from typing import Any, List, Dict
from pathlib import Path


class StatsComputer:
    """统计计算器类"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.data_path = config['data']['npy_data_path']
        self.label_path = config['data']['npy_label_path']
        self.label_map_path = config['data']['label_map_path']
    
    def compute_action_prototypes(self, training_samples: List[Dict], num_classes: int) -> torch.Tensor:
        """计算动作原型特征"""
        if not training_samples:
            raise ValueError("训练样本为空")
        
        # 获取特征维度
        feature_dim = training_samples[0]['features'].shape[0] if 'features' in training_samples[0] else 64
        
        proto_sum = torch.zeros((num_classes, feature_dim), dtype=torch.float32)
        proto_count = torch.zeros(num_classes, dtype=torch.float32)
        
        # 遍历训练样本计算每个动作类别的特征均值
        for sample in training_samples:
            if 'features' in sample and 'current_action' in sample:
                features = torch.tensor(sample['features'], dtype=torch.float32)
                current_action = sample['current_action']
                
                if 0 <= current_action < num_classes:
                    proto_sum[current_action] += features
                    proto_count[current_action] += 1
        
        # 计算原型，对于没有数据的动作类别使用零向量
        prototypes = torch.zeros((num_classes, feature_dim), dtype=torch.float32)
        for n_k in range(num_classes):
            if proto_count[n_k] > 0:
                prototypes[n_k] = proto_sum[n_k] / proto_count[n_k]
        
        return prototypes
    
    def compute_transition_matrix(self, training_samples: List[Dict], num_classes: int, alpha: float = 1.0) -> torch.Tensor:
        """计算转移概率矩阵"""
        transitions = defaultdict(int)
        totals = defaultdict(int)
        
        for sample in training_samples:
            if 'current_action' in sample and 'next_action' in sample:
                u = sample['current_action']
                v = sample['next_action']
                
                if 0 <= u < num_classes and 0 <= v < num_classes:
                    transitions[(u, v)] += 1
                    totals[u] += 1
        
        # 构建权重矩阵
        W0 = torch.zeros((num_classes, num_classes), dtype=torch.float32)
        
        for i in range(num_classes):
            total = totals.get(i, 0)
            for j in range(num_classes):
                count = transitions.get((i, j), 0)
                # 计算平滑后的概率并转换为logits (Laplace smoothing)
                prob = (count + alpha) / (total + num_classes * alpha) if total > 0 else 1.0 / num_classes
                W0[i, j] = torch.log(torch.tensor(prob))
        
        return W0
    
    def compute_statistics(self, training_samples: List[Dict], num_classes: int) -> Dict[str, torch.Tensor]:
        """计算所有统计信息"""
        prototypes = self.compute_action_prototypes(training_samples, num_classes)
        transition_matrix = self.compute_transition_matrix(training_samples, num_classes)
        
        return {
            'prototypes': prototypes,
            'transition_matrix': transition_matrix
        }


def compute_and_save_stats(dataset: Any, M: int, save_dir: str):
    """
    计算并保存统计数据
    
    Args:
        dataset: 一个配置为 'train' split 的 CerealsDataset 实例
        M: 动作类别总数
        save_dir: 保存统计结果的目录
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # 1. 计算原型 (Prototypes)
    # 鲁棒地获取特征维度D，避免首个样本为空的错误
    D = -1
    for V_seq_sample, _ in dataset:
        if V_seq_sample is not None and len(V_seq_sample) > 0:
            D = V_seq_sample.shape[1]
            break
    
    if D == -1:
        raise ValueError("Could not determine feature dimension from the dataset.")

    proto_sum = torch.zeros((M, D), dtype=torch.float32)
    proto_count = torch.zeros(M, dtype=torch.float32)
    
    print(f"Computing prototypes with D={D}, M={M}...")
    
    # 遍历数据集计算每个动作类别的特征均值
    for i, (V_seq, y_seq) in enumerate(dataset):
        if V_seq is None: 
            continue
            
        for n_k in range(M):
            mask = (y_seq == n_k)
            if mask.any():
                proto_sum[n_k] += V_seq[mask].sum(dim=0)
                proto_count[n_k] += mask.sum()
        
        if (i + 1) % 100 == 0:
            print(f"Processed {i + 1} samples...")
    
    # 计算原型，对于没有数据的动作类别使用零向量
    prototypes = torch.zeros((M, D), dtype=torch.float32)
    for n_k in range(M):
        if proto_count[n_k] > 0:
            prototypes[n_k] = proto_sum[n_k] / proto_count[n_k]
        # 没有数据的动作类别保持零向量
    
    print(f"Computed prototypes for {(proto_count > 1).sum().item()} action classes")
    
    # 2. 计算初始图权重 (W0)
    print("Computing initial graph weights...")
    
    transitions = defaultdict(int)
    totals = defaultdict(int)
    
    for i, (_, y_seq) in enumerate(dataset):
        if y_seq is None: 
            continue
            
        for j in range(len(y_seq) - 1):
            u, v = y_seq[j].item(), y_seq[j+1].item()
            transitions[(u, v)] += 1
            totals[u] += 1
        
        if (i + 1) % 100 == 0:
            print(f"Processed {i + 1} samples for transitions...")
            
    W0 = torch.zeros((M, M), dtype=torch.float32)
    epsilon = 1e-9
    
    for i in range(M):
        total = totals.get(i, 0)
        for j in range(M):
            count = transitions.get((i, j), 0)
            # 计算平滑后的概率并转换为logits
            prob = (count + epsilon) / (total + M * epsilon)
            W0[i, j] = torch.log(torch.tensor(prob))

    print(f"Computed transition matrix with {len(transitions)} unique transitions")

    # 3. 保存结果
    prototypes_path = os.path.join(save_dir, "prototypes.pt")
    w0_path = os.path.join(save_dir, "W0.pt")
    
    torch.save(prototypes, prototypes_path)
    torch.save(W0, w0_path)
    
    print(f"Prototypes (shape: {prototypes.shape}) saved to {prototypes_path}")
    print(f"W0 (shape: {W0.shape}) saved to {w0_path}")
    
    # 保存一些统计信息
    stats_info = {
        'feature_dim': D,
        'num_actions': M,
        'num_transitions': len(transitions),
        'actions_with_data': (proto_count > 1).sum().item(),
        'total_frames': sum(proto_count).item()
    }
    
    stats_path = os.path.join(save_dir, "stats_info.txt")
    with open(stats_path, 'w') as f:
        for key, value in stats_info.items():
            f.write(f"{key}: {value}\n")
    
    print(f"Statistics info saved to {stats_path}")
    return prototypes, W0


def load_stats(stats_dir: str):
    """
    加载预计算的统计数据
    
    Args:
        stats_dir: 统计数据目录
        
    Returns:
        (prototypes, W0) 元组
    """
    prototypes_path = os.path.join(stats_dir, "prototypes.pt")
    w0_path = os.path.join(stats_dir, "W0.pt")
    
    if not os.path.exists(prototypes_path):
        raise FileNotFoundError(f"Prototypes file not found: {prototypes_path}")
    if not os.path.exists(w0_path):
        raise FileNotFoundError(f"W0 file not found: {w0_path}")
    
    prototypes = torch.load(prototypes_path)
    W0 = torch.load(w0_path)
    
    print(f"Loaded prototypes: {prototypes.shape}")
    print(f"Loaded W0: {W0.shape}")
    
    return prototypes, W0


def verify_stats(prototypes: torch.Tensor, W0: torch.Tensor, expected_D: int, expected_M: int):
    """
    验证统计数据的形状和有效性
    
    Args:
        prototypes: 原型矩阵
        W0: 初始权重矩阵
        expected_D: 期望的特征维度
        expected_M: 期望的动作类别数
    """
    assert prototypes.shape == (expected_M, expected_D), f"Prototypes shape mismatch: {prototypes.shape} vs ({expected_M}, {expected_D})"
    assert W0.shape == (expected_M, expected_M), f"W0 shape mismatch: {W0.shape} vs ({expected_M}, {expected_M})"
    
    # 检查是否有NaN或Inf
    assert not torch.isnan(prototypes).any(), "Prototypes contain NaN values"
    assert not torch.isinf(prototypes).any(), "Prototypes contain Inf values"
    assert not torch.isnan(W0).any(), "W0 contains NaN values"
    assert not torch.isinf(W0).any(), "W0 contains Inf values"
    
    print("Statistics verification passed")


def compute_and_save_stats_from_samples(training_samples: List[Dict], M: int, save_dir: str):
    """
    从训练样本计算并保存统计数据

    Args:
        training_samples: 训练样本列表，每个样本包含features, current_action, next_action
        M: 动作类别总数
        save_dir: 保存统计结果的目录
    """
    os.makedirs(save_dir, exist_ok=True)

    if not training_samples:
        raise ValueError("训练样本为空")

    # 1. 计算原型 (Prototypes)
    # 获取特征维度
    D = training_samples[0]['features'].shape[0] if 'features' in training_samples[0] else 64

    proto_sum = torch.zeros((M, D), dtype=torch.float32)
    proto_count = torch.zeros(M, dtype=torch.float32)

    print(f"Computing prototypes with D={D}, M={M}...")

    # 遍历训练样本计算每个动作类别的特征均值
    for i, sample in enumerate(training_samples):
        if 'features' in sample and 'current_action' in sample:
            features = torch.tensor(sample['features'], dtype=torch.float32)
            current_action = sample['current_action']

            if 0 <= current_action < M:
                proto_sum[current_action] += features
                proto_count[current_action] += 1

        if (i + 1) % 1000 == 0:
            print(f"Processed {i + 1} samples...")

    # 计算原型，对于没有数据的动作类别使用零向量
    prototypes = torch.zeros((M, D), dtype=torch.float32)
    for n_k in range(M):
        if proto_count[n_k] > 0:
            prototypes[n_k] = proto_sum[n_k] / proto_count[n_k]
        # 没有数据的动作类别保持零向量

    print(f"Computed prototypes for {(proto_count > 0).sum().item()} action classes")

    # 2. 计算初始图权重 (W0)
    print("Computing initial graph weights...")

    transitions = defaultdict(int)
    totals = defaultdict(int)

    for i, sample in enumerate(training_samples):
        if 'current_action' in sample and 'next_action' in sample:
            u = sample['current_action']
            v = sample['next_action']

            if 0 <= u < M and 0 <= v < M:
                transitions[(u, v)] += 1
                totals[u] += 1

        if (i + 1) % 1000 == 0:
            print(f"Processed {i + 1} samples for transitions...")

    W0 = torch.zeros((M, M), dtype=torch.float32)
    epsilon = 1e-9

    for i in range(M):
        total = totals.get(i, 0)
        for j in range(M):
            count = transitions.get((i, j), 0)
            # 计算平滑后的概率并转换为logits
            prob = (count + epsilon) / (total + M * epsilon)
            W0[i, j] = torch.log(torch.tensor(prob))

    print(f"Computed transition matrix with {len(transitions)} unique transitions")

    # 3. 保存结果
    prototypes_path = os.path.join(save_dir, "prototypes.pt")
    w0_path = os.path.join(save_dir, "W0.pt")

    torch.save(prototypes, prototypes_path)
    torch.save(W0, w0_path)

    print(f"Prototypes (shape: {prototypes.shape}) saved to {prototypes_path}")
    print(f"W0 (shape: {W0.shape}) saved to {w0_path}")

    # 保存一些统计信息
    stats_info = {
        'feature_dim': D,
        'num_actions': M,
        'num_transitions': len(transitions),
        'actions_with_data': (proto_count > 0).sum().item(),
        'total_samples': len(training_samples)
    }

    stats_path = os.path.join(save_dir, "stats_info.txt")
    with open(stats_path, 'w') as f:
        for key, value in stats_info.items():
            f.write(f"{key}: {value}\n")

    print(f"Statistics info saved to {stats_path}")
    return prototypes, W0


if __name__ == "__main__":
    """
    支持 python -m src.utils.stats_computer 命令
    用于离线计算和保存统计数据
    """
    import sys
    sys.path.insert(0, '.')

    from src.utils.data_loader import BreakfastDataLoader
    import yaml

    # 加载配置
    config_path = Path(__file__).parent.parent.parent / "configs" / "config.yaml"
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
    else:
        # 使用默认配置
        config = {
            'data': {
                'raw_data_path': "/data2/syd_data/Breakfast_Data/breakfast_data",
                'raw_label_path': "/data2/syd_data/Breakfast_Data/segmentation_coarse",
                'npy_data_path': "/data2/syd_data/Breakfast_Data/breakfast_data_npy",
                'npy_label_path': "/data2/syd_data/Breakfast_Data/segmentation_coarse_npy",
                'label_map_path': "/data2/syd_data/Breakfast_Data/label_map.json"
            },
            'training': {
                'train_splits': ["s1", "s2", "s3"],
                'test_splits': ["s4"]
            }
        }

    print("Creating data loader for statistics computation...")
    data_loader = BreakfastDataLoader(config)

    # 获取所有训练数据
    training_samples = data_loader.get_all_training_data()
    print(f"Data loader created with {len(training_samples)} training samples")

    if len(training_samples) == 0:
        raise ValueError("数据集为空，请检查数据路径配置。")

    # 获取动作类别数
    M = data_loader.num_classes
    save_dir = "./stats"

    # 计算并保存统计数据
    prototypes, W0 = compute_and_save_stats_from_samples(training_samples, M, save_dir)

    # 验证统计数据
    D = prototypes.shape[1]
    verify_stats(prototypes, W0, D, M)

    print("Statistics computation completed successfully!")
