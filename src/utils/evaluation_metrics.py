"""评估指标计算工具"""
import numpy as np
from typing import List, Dict, Tuple, Optional
from scipy.optimize import linear_sum_assignment
from scipy.stats import bootstrap
import editdistance

class SegmentEvaluator:
    """段级评估器"""
    
    def __init__(self, iou_thresholds: List[float] = [0.5, 0.75, 0.9]):
        self.iou_thresholds = iou_thresholds
        self.sil_id = 0  # SIL类别的ID
    
    def segments_from_labels(self, labels: np.ndarray) -> List[Tuple[int, int, int]]:
        """从标签序列提取段信息"""
        if len(labels) == 0:
            return []
        
        segments = []
        current_label = labels[0]
        start_frame = 0
        
        for i in range(1, len(labels)):
            if labels[i] != current_label:
                # 段结束
                segments.append((start_frame, i - 1, current_label))
                start_frame = i
                current_label = labels[i]
        
        # 添加最后一段
        segments.append((start_frame, len(labels) - 1, current_label))
        
        return segments
    
    def filter_sil_segments(self, segments: List[Tuple[int, int, int]]) -> List[Tuple[int, int, int]]:
        """过滤SIL段"""
        return [seg for seg in segments if seg[2] != self.sil_id]
    
    def compute_segment_iou(self, pred_seg: Tuple[int, int, int], 
                           gt_seg: Tuple[int, int, int]) -> float:
        """计算两个段的IoU"""
        pred_start, pred_end, pred_label = pred_seg
        gt_start, gt_end, gt_label = gt_seg
        
        # 如果标签不同，IoU为0
        if pred_label != gt_label:
            return 0.0
        
        # 计算交集
        intersection_start = max(pred_start, gt_start)
        intersection_end = min(pred_end, gt_end)
        intersection = max(0, intersection_end - intersection_start + 1)
        
        # 计算并集
        union = (pred_end - pred_start + 1) + (gt_end - gt_start + 1) - intersection
        
        return intersection / union if union > 0 else 0.0
    
    def compute_temporal_iou_at_threshold(self, pred_segments: List[Tuple[int, int, int]], 
                                        gt_segments: List[Tuple[int, int, int]], 
                                        threshold: float) -> Dict[str, float]:
        """计算指定阈值下的时序IoU指标"""
        # 过滤SIL段
        pred_segments = self.filter_sil_segments(pred_segments)
        gt_segments = self.filter_sil_segments(gt_segments)
        
        if not pred_segments and not gt_segments:
            return {'precision': 1.0, 'recall': 1.0, 'f1': 1.0}
        
        if not pred_segments:
            return {'precision': 0.0, 'recall': 0.0, 'f1': 0.0}
        
        if not gt_segments:
            return {'precision': 0.0, 'recall': 0.0, 'f1': 0.0}
        
        # 构建IoU矩阵
        iou_matrix = np.zeros((len(gt_segments), len(pred_segments)))
        for i, gt_seg in enumerate(gt_segments):
            for j, pred_seg in enumerate(pred_segments):
                iou_matrix[i, j] = self.compute_segment_iou(pred_seg, gt_seg)
        
        # 使用匈牙利算法进行最优匹配
        cost_matrix = 1 - iou_matrix
        row_indices, col_indices = linear_sum_assignment(cost_matrix)
        
        # 计算TP, FP, FN
        tp = 0
        for row, col in zip(row_indices, col_indices):
            if iou_matrix[row, col] >= threshold:
                tp += 1
        
        fp = len(pred_segments) - tp
        fn = len(gt_segments) - tp
        
        # 计算精确率、召回率和F1分数
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
        
        return {'precision': precision, 'recall': recall, 'f1': f1}

class FrameEvaluator:
    """帧级评估器"""
    
    def __init__(self):
        self.sil_id = 0
    
    def compute_frame_accuracy(self, pred_labels: np.ndarray, 
                              gt_labels: np.ndarray, 
                              exclude_sil: bool = True) -> float:
        """计算帧级准确率"""
        if len(pred_labels) != len(gt_labels):
            raise ValueError("Prediction and ground truth must have same length")
        
        if exclude_sil:
            # 排除SIL帧
            mask = gt_labels != self.sil_id
            if not np.any(mask):
                return 1.0  # 如果全是SIL，返回1.0
            pred_labels = pred_labels[mask]
            gt_labels = gt_labels[mask]
        
        correct = np.sum(pred_labels == gt_labels)
        total = len(gt_labels)
        
        return correct / total if total > 0 else 0.0

class SequenceEvaluator:
    """序列级评估器"""
    
    def __init__(self):
        self.sil_id = 0
    
    def merge_consecutive_sil(self, segments: List[Tuple[int, int, int]]) -> List[Tuple[int, int, int]]:
        """合并连续的SIL段"""
        if not segments:
            return []
        
        merged = []
        current_start, current_end, current_label = segments[0]
        
        for start, end, label in segments[1:]:
            if label == self.sil_id and current_label == self.sil_id:
                # 合并连续的SIL段
                current_end = end
            else:
                merged.append((current_start, current_end, current_label))
                current_start, current_end, current_label = start, end, label
        
        merged.append((current_start, current_end, current_label))
        return merged
    
    def segments_to_sequence(self, segments: List[Tuple[int, int, int]]) -> List[int]:
        """将段转换为动作序列（排除SIL）"""
        sequence = []
        for start, end, label in segments:
            if label != self.sil_id:
                sequence.append(label)
        return sequence
    
    def compute_edit_distance(self, pred_segments: List[Tuple[int, int, int]], 
                            gt_segments: List[Tuple[int, int, int]]) -> float:
        """计算归一化编辑距离"""
        # 合并连续SIL段
        pred_segments = self.merge_consecutive_sil(pred_segments)
        gt_segments = self.merge_consecutive_sil(gt_segments)
        
        # 转换为动作序列
        pred_sequence = self.segments_to_sequence(pred_segments)
        gt_sequence = self.segments_to_sequence(gt_segments)
        
        if not gt_sequence:
            return 0.0 if not pred_sequence else 1.0
        
        # 计算编辑距离
        edit_dist = editdistance.eval(pred_sequence, gt_sequence)
        normalized_dist = edit_dist / len(gt_sequence)
        
        return normalized_dist
    
    def compute_action_coverage(self, pred_segments: List[Tuple[int, int, int]], 
                              gt_segments: List[Tuple[int, int, int]]) -> float:
        """计算动作覆盖率"""
        pred_actions = set()
        gt_actions = set()
        
        for _, _, label in pred_segments:
            if label != self.sil_id:
                pred_actions.add(label)
        
        for _, _, label in gt_segments:
            if label != self.sil_id:
                gt_actions.add(label)
        
        if not gt_actions:
            return 1.0 if not pred_actions else 0.0
        
        coverage = len(pred_actions & gt_actions) / len(gt_actions)
        return coverage

class EvaluationMetrics:
    """完整的评估指标计算器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.iou_thresholds = config['evaluation']['iou_thresholds']
        
        # 子评估器
        self.segment_evaluator = SegmentEvaluator(self.iou_thresholds)
        self.frame_evaluator = FrameEvaluator()
        self.sequence_evaluator = SequenceEvaluator()
    
    def evaluate_single_video(self, pred_labels: np.ndarray, 
                            gt_labels: np.ndarray) -> Dict[str, float]:
        """评估单个视频的所有指标"""
        # 提取段信息
        pred_segments = self.segment_evaluator.segments_from_labels(pred_labels)
        gt_segments = self.segment_evaluator.segments_from_labels(gt_labels)
        
        results = {}
        
        # 帧级准确率
        results['frame_accuracy'] = self.frame_evaluator.compute_frame_accuracy(pred_labels, gt_labels)
        
        # 时序IoU（多个阈值）
        for threshold in self.iou_thresholds:
            iou_results = self.segment_evaluator.compute_temporal_iou_at_threshold(
                pred_segments, gt_segments, threshold
            )
            results[f'iou_{threshold}_precision'] = iou_results['precision']
            results[f'iou_{threshold}_recall'] = iou_results['recall']
            results[f'iou_{threshold}_f1'] = iou_results['f1']
        
        # 编辑距离
        results['edit_distance'] = self.sequence_evaluator.compute_edit_distance(pred_segments, gt_segments)
        
        # 动作覆盖率
        results['action_coverage'] = self.sequence_evaluator.compute_action_coverage(pred_segments, gt_segments)
        
        return results
    
    def bootstrap_confidence_interval(self, video_results: List[Dict[str, float]], 
                                    metric_name: str, 
                                    n_bootstrap: int = 500, 
                                    confidence: float = 0.95) -> Tuple[float, float, float]:
        """计算Bootstrap置信区间"""
        if not video_results:
            return 0.0, 0.0, 0.0
        
        # 提取指定指标的值
        metric_values = [result[metric_name] for result in video_results if metric_name in result]
        
        if not metric_values:
            return 0.0, 0.0, 0.0
        
        metric_values = np.array(metric_values)
        
        # Bootstrap重采样
        rng = np.random.default_rng(42)
        bootstrap_means = []
        
        for _ in range(n_bootstrap):
            sample_indices = rng.choice(len(metric_values), len(metric_values), replace=True)
            bootstrap_sample = metric_values[sample_indices]
            bootstrap_means.append(np.mean(bootstrap_sample))
        
        # 计算置信区间
        alpha = 1 - confidence
        lower_percentile = (alpha / 2) * 100
        upper_percentile = (1 - alpha / 2) * 100
        
        mean_estimate = np.mean(bootstrap_means)
        ci_lower = np.percentile(bootstrap_means, lower_percentile)
        ci_upper = np.percentile(bootstrap_means, upper_percentile)
        
        return float(mean_estimate), float(ci_lower), float(ci_upper)
    
    def evaluate_all_videos(self, predictions: Dict[str, np.ndarray], 
                          ground_truth: Dict[str, np.ndarray]) -> Dict[str, Dict]:
        """评估所有视频并计算统计信息"""
        video_results = []
        
        # 评估每个视频
        for video_id in predictions:
            if video_id in ground_truth:
                pred_labels = predictions[video_id]
                gt_labels = ground_truth[video_id]
                
                video_result = self.evaluate_single_video(pred_labels, gt_labels)
                video_result['video_id'] = video_id
                video_results.append(video_result)
        
        # 计算总体统计
        summary_results = {}
        
        if video_results:
            # 获取所有指标名称
            metric_names = set()
            for result in video_results:
                metric_names.update(result.keys())
            metric_names.discard('video_id')
            
            # 为每个指标计算统计信息
            for metric_name in metric_names:
                mean_val, ci_lower, ci_upper = self.bootstrap_confidence_interval(
                    video_results, metric_name
                )
                
                summary_results[metric_name] = {
                    'mean': mean_val,
                    'ci_lower': ci_lower,
                    'ci_upper': ci_upper,
                    'point_estimate': mean_val
                }
        
        return {
            'summary': summary_results,
            'per_video': video_results
        }
